# 蓝图连接游戏 (Blueprint Connection Game)

一个基于React和TypeScript的蓝图连接游戏，类似于Unreal Engine的蓝图系统。玩家需要通过连接不同形状和颜色的端口来构建有效的数据流图。

## 🎮 游戏特性

### 核心功能
- **智能节点系统**: 包含起点、终点和处理节点，支持智能生成
- **多样化端口系统**: 不同形状（方形、菱形、三角形、圆形）和颜色的端口
- **智能连接规则**: 只能连接相同形状和颜色的端口，且输入端口只能连接输出端口
- **流畅拖拽交互**: 支持节点拖拽和连接线绘制，优化的用户体验
- **数据流动画**: 可视化数据在节点间的流动过程

### 游戏模式 ✨ 新增
1. **回合制模式**: 逐步生成新节点，策略性地构建蓝图
2. **俄罗斯方块模式**: 自动掉落节点机制，增加时间压力和挑战性

### 智能验证系统 🧠 增强
- **有向无环图检查**: 确保连接不会形成循环依赖
- **可达性验证**: 检查所有终点节点是否可从起点到达
- **完整性检查**: 验证所有必要端口是否已连接
- **实时错误诊断**: 提供详细的问题分析和改进建议
- **智能可解性验证**: 确保生成的节点池始终可解

### 进度系统 📊 新增
- **智能评分算法**: 基于连接复杂度、时间效率和端口多样性
- **动态难度调整**: 根据等级自动调整游戏参数
- **关卡进度管理**: 自动检测完成条件，无缝进入下一关
- **时间奖励机制**: 快速完成获得额外分数

## 🚀 快速开始

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

## 🎯 游戏玩法

### 基本操作
1. **选择临时节点**: 在右侧临时区域点击选择节点
2. **放置节点**: 在游戏画布上点击放置选中的节点
3. **创建连接**: 从输出端口（右侧）拖拽到输入端口（左侧）
4. **删除连接**: 点击连接线删除连接
5. **移动节点**: 拖拽节点改变位置

### 连接规则
- ✅ 相同形状和颜色的端口可以连接
- ✅ 输出端口（右侧）连接到输入端口（左侧）
- ❌ 不能连接到同一个节点
- ❌ 端口不能重复连接
- ❌ 不能形成循环依赖

### 获胜条件
- 所有必要的端口都已连接
- 形成有效的有向无环图
- 所有终点节点都可从起点到达

## 🏗️ 项目结构

```
src/
├── components/          # React组件
│   ├── GameCanvas.tsx   # 主游戏画布
│   ├── GameNode.tsx     # 节点组件
│   ├── Port.tsx         # 端口组件
│   ├── Connection.tsx   # 连接线组件
│   ├── DataFlowAnimation.tsx  # 数据流动画
│   ├── GameControls.tsx # 游戏控制面板
│   └── TemporaryNodeArea.tsx  # 临时节点区域
├── types/               # TypeScript类型定义
│   └── index.ts
├── utils/               # 工具函数
│   ├── gameLogic.ts     # 游戏逻辑
│   └── nodeGenerator.ts # 节点生成器
├── App.tsx              # 主应用组件
└── main.tsx             # 应用入口
```

## 🎨 技术栈

- **React 19**: 用户界面框架
- **TypeScript**: 类型安全的JavaScript
- **Vite 6.3.5**: 快速的构建工具
- **Vitest**: 单元测试框架 ✨ 新增
- **@dnd-kit**: 拖拽交互库
- **SVG**: 矢量图形绘制
- **CSS3**: 样式和动画

## 🔧 开发功能

### 已实现功能 ✨ 大幅增强
- ✅ **智能节点系统**（起点、终点、处理节点）
- ✅ **多样化端口系统**（不同形状和颜色）
- ✅ **流畅拖拽交互**（节点移动、连接创建）
- ✅ **智能连接验证**（形状、颜色、方向匹配）
- ✅ **高级图论算法**（环检测、拓扑排序、可达性分析）
- ✅ **数据流动画系统**
- ✅ **完整游戏状态管理**
- ✅ **智能节点生成算法** ✨ 新增
- ✅ **双游戏模式系统** ✨ 新增
- ✅ **智能评分系统** ✨ 新增
- ✅ **动态难度调整** ✨ 新增
- ✅ **实时错误诊断** ✨ 新增
- ✅ **关卡进度管理** ✨ 新增
- ✅ **完整单元测试** ✨ 新增

### 核心算法改进 🧠
- **智能节点池生成**: 确保生成的节点池始终可解
- **桥接节点算法**: 自动创建连接路径
- **可解性验证**: 实时检查游戏状态
- **俄罗斯方块掉落**: 时间控制的节点生成
- **综合评分算法**: 多维度评分机制

### 未来计划 🔮
- 🔄 特殊功能节点（分支、合并、转换）
- 🔄 节点动态变化机制
- 🔄 成就系统和奖励
- 🔄 音效和高级视觉效果
- 🔄 保存/加载功能
- 🔄 多人协作/竞争模式

## 🎮 游戏截图

游戏包含三个主要区域：
1. **左侧控制面板**: 游戏模式选择、控制按钮、状态显示
2. **中央游戏画布**: 节点放置和连接的主要区域
3. **右侧临时区域**: 待放置的节点预览

## 🤝 贡献

欢迎提交Issue和Pull Request来改进游戏！

## 📄 许可证

MIT License
