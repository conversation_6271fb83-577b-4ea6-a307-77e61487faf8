import { describe, it, expect } from 'vitest';
import { intelligentNodeGenerator } from '../utils/intelligentNodeGenerator';
import { validateGameState } from '../utils/gameLogic';
import { NodeType, PortDirection } from '../types';
import type { GameConfig } from '../types';

describe('IntelligentNodeGenerator', () => {
  describe('generateSolvableLevel', () => {
    it('should generate a solvable level with correct node counts', () => {
      const config: GameConfig = {
        startNodeCount: 2,
        endNodeCount: 2,
        maxTempNodes: 5,
        allowLineCrossing: true
      };
      
      const level = intelligentNodeGenerator.generateSolvableLevel(config);
      
      expect(level.startNodes).toHaveLength(2);
      expect(level.endNodes).toHaveLength(2);
      expect(level.processNodes.length).toBeGreaterThan(0);
      expect(level.solution.length).toBeGreaterThan(0);
    });

    it('should generate nodes with correct types', () => {
      const config: GameConfig = {
        startNodeCount: 1,
        endNodeCount: 1,
        maxTempNodes: 3,
        allowLineCrossing: true
      };
      
      const level = intelligentNodeGenerator.generateSolvableLevel(config);
      
      // Check start nodes
      level.startNodes.forEach(node => {
        expect(node.type).toBe(NodeType.START);
        expect(node.inputPorts).toHaveLength(0);
        expect(node.outputPorts.length).toBeGreaterThan(0);
      });
      
      // Check end nodes
      level.endNodes.forEach(node => {
        expect(node.type).toBe(NodeType.END);
        expect(node.outputPorts).toHaveLength(0);
        expect(node.inputPorts.length).toBeGreaterThan(0);
      });
      
      // Check process nodes
      level.processNodes.forEach(node => {
        expect(node.type).toBe(NodeType.PROCESS);
      });
    });

    it('should generate a valid solution', () => {
      const config: GameConfig = {
        startNodeCount: 1,
        endNodeCount: 1,
        maxTempNodes: 3,
        allowLineCrossing: true
      };
      
      const level = intelligentNodeGenerator.generateSolvableLevel(config);
      const allNodes = [...level.startNodes, ...level.endNodes, ...level.processNodes];
      
      // Verify solution connections are valid
      for (const connection of level.solution) {
        const fromPort = allNodes
          .flatMap(node => [...node.inputPorts, ...node.outputPorts])
          .find(port => port.id === connection.fromPortId);
        
        const toPort = allNodes
          .flatMap(node => [...node.inputPorts, ...node.outputPorts])
          .find(port => port.id === connection.toPortId);
        
        expect(fromPort).toBeDefined();
        expect(toPort).toBeDefined();
        expect(fromPort!.direction).toBe(PortDirection.OUTPUT);
        expect(toPort!.direction).toBe(PortDirection.INPUT);
        expect(fromPort!.shape).toBe(toPort!.shape);
        expect(fromPort!.color).toBe(toPort!.color);
      }
    });

    it('should generate a level that passes validation', () => {
      const config: GameConfig = {
        startNodeCount: 2,
        endNodeCount: 2,
        maxTempNodes: 4,
        allowLineCrossing: true
      };
      
      const level = intelligentNodeGenerator.generateSolvableLevel(config);
      const allNodes = [...level.startNodes, ...level.endNodes, ...level.processNodes];
      
      // Create connections based on solution
      const connections = level.solution.map(sol => ({
        id: `conn-${sol.fromPortId}-${sol.toPortId}`,
        fromPortId: sol.fromPortId,
        toPortId: sol.toPortId,
        fromNodeId: allNodes.find(n => 
          [...n.inputPorts, ...n.outputPorts].some(p => p.id === sol.fromPortId)
        )!.id,
        toNodeId: allNodes.find(n => 
          [...n.inputPorts, ...n.outputPorts].some(p => p.id === sol.toPortId)
        )!.id
      }));
      
      // Mark ports as connected
      const nodesWithConnections = allNodes.map(node => ({
        ...node,
        inputPorts: node.inputPorts.map(port => ({
          ...port,
          isConnected: connections.some(c => c.toPortId === port.id)
        })),
        outputPorts: node.outputPorts.map(port => ({
          ...port,
          isConnected: connections.some(c => c.fromPortId === port.id)
        }))
      }));
      
      const validation = validateGameState(nodesWithConnections, connections, true);

      // Debug output if validation fails
      if (!validation.isValid) {
        console.log('Validation errors:', validation.errors);
        console.log('Nodes:', nodesWithConnections.map(n => ({
          id: n.id,
          type: n.type,
          inputPorts: n.inputPorts.map(p => ({ id: p.id, isConnected: p.isConnected })),
          outputPorts: n.outputPorts.map(p => ({ id: p.id, isConnected: p.isConnected }))
        })));
        console.log('Connections:', connections);
      }

      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    it('should handle different configuration sizes', () => {
      const configs: GameConfig[] = [
        { startNodeCount: 1, endNodeCount: 1, maxTempNodes: 2, allowLineCrossing: true },
        { startNodeCount: 3, endNodeCount: 2, maxTempNodes: 6, allowLineCrossing: true },
        { startNodeCount: 2, endNodeCount: 3, maxTempNodes: 8, allowLineCrossing: false }
      ];
      
      configs.forEach(config => {
        const level = intelligentNodeGenerator.generateSolvableLevel(config);
        
        expect(level.startNodes).toHaveLength(config.startNodeCount);
        expect(level.endNodes).toHaveLength(config.endNodeCount);
        expect(level.processNodes.length).toBeLessThanOrEqual(config.maxTempNodes);
        expect(level.solution.length).toBeGreaterThan(0);
      });
    });

    it('should generate unique node and port IDs', () => {
      const config: GameConfig = {
        startNodeCount: 2,
        endNodeCount: 2,
        maxTempNodes: 4,
        allowLineCrossing: true
      };
      
      const level = intelligentNodeGenerator.generateSolvableLevel(config);
      const allNodes = [...level.startNodes, ...level.endNodes, ...level.processNodes];
      
      // Check unique node IDs
      const nodeIds = allNodes.map(node => node.id);
      const uniqueNodeIds = new Set(nodeIds);
      expect(uniqueNodeIds.size).toBe(nodeIds.length);
      
      // Check unique port IDs
      const portIds = allNodes.flatMap(node => 
        [...node.inputPorts, ...node.outputPorts].map(port => port.id)
      );
      const uniquePortIds = new Set(portIds);
      expect(uniquePortIds.size).toBe(portIds.length);
    });

    it('should ensure all end node inputs have a path from start nodes', () => {
      const config: GameConfig = {
        startNodeCount: 1,
        endNodeCount: 2,
        maxTempNodes: 4,
        allowLineCrossing: true
      };
      
      const level = intelligentNodeGenerator.generateSolvableLevel(config);
      
      // Count end node input ports
      const endInputPortCount = level.endNodes.reduce(
        (sum, node) => sum + node.inputPorts.length, 
        0
      );
      
      // Each end input port should have at least one connection in the solution
      const endInputConnections = level.solution.filter(sol => {
        const toPort = level.endNodes
          .flatMap(node => node.inputPorts)
          .find(port => port.id === sol.toPortId);
        return !!toPort;
      });
      
      expect(endInputConnections.length).toBe(endInputPortCount);
    });
  });
});
