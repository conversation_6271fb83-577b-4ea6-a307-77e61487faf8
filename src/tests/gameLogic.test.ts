import { describe, it, expect } from 'vitest';
import {
  calculateScore,
  checkGameCompletion,
  generateNextLevel,
  shouldDropNewNode,
  calculateDropInterval,
  isGameSolvable,
  areAllPortsConnected
} from '../utils/gameLogic';
import {
  generateSolvableNodePool,
  createStartNode,
  createEndNode
} from '../utils/nodeGenerator';
import { NodeType, PortShape, PortColor, PortDirection } from '../types';
import type { GameNode, Connection } from '../types';

describe('Game Logic Tests', () => {
  describe('calculateScore', () => {
    it('should calculate basic score correctly', () => {
      const nodes: GameNode[] = [
        {
          id: '1',
          type: NodeType.START,
          position: { x: 0, y: 0 },
          inputPorts: [],
          outputPorts: [{
            id: 'p1',
            shape: PortShape.SQUARE,
            color: PortColor.RED,
            direction: PortDirection.OUTPUT,
            nodeId: '1',
            position: { x: 0, y: 0 },
            isConnected: true
          }]
        }
      ];
      
      const connections: Connection[] = [
        {
          id: 'c1',
          fromPortId: 'p1',
          toPortId: 'p2',
          fromNodeId: '1',
          toNodeId: '2'
        }
      ];
      
      const score = calculateScore(nodes, connections, 1, 0);
      expect(score).toBeGreaterThan(0);
    });
  });

  describe('generateNextLevel', () => {
    it('should generate appropriate level configuration', () => {
      const level1 = generateNextLevel(1);
      expect(level1.startNodeCount).toBe(1);
      expect(level1.endNodeCount).toBe(1);
      expect(level1.maxTempNodes).toBe(3);
      expect(level1.timeLimit).toBeUndefined();

      const level10 = generateNextLevel(10);
      expect(level10.timeLimit).toBeDefined();
      expect(level10.timeLimit).toBeLessThan(180);
    });
  });

  describe('shouldDropNewNode', () => {
    it('should determine when to drop new nodes correctly', () => {
      const now = Date.now();
      
      // Should drop when conditions are met
      const shouldDrop = shouldDropNewNode(2, 5, 1000, now - 2000);
      expect(shouldDrop).toBe(true);
      
      // Should not drop when max nodes reached
      const shouldNotDrop = shouldDropNewNode(5, 5, 1000, now - 2000);
      expect(shouldNotDrop).toBe(false);
      
      // Should not drop when time hasn't passed
      const shouldNotDropTime = shouldDropNewNode(2, 5, 1000, now - 500);
      expect(shouldNotDropTime).toBe(false);
    });
  });

  describe('calculateDropInterval', () => {
    it('should calculate drop intervals correctly', () => {
      const level1Interval = calculateDropInterval(1);
      const level5Interval = calculateDropInterval(5);
      
      expect(level1Interval).toBe(5000);
      expect(level5Interval).toBeLessThan(level1Interval);
      expect(level5Interval).toBeGreaterThanOrEqual(1000);
    });
  });

  describe('generateSolvableNodePool', () => {
    it('should generate a solvable node pool', () => {
      const startNode = createStartNode({ x: 0, y: 0 });
      const endNode = createEndNode({ x: 100, y: 0 });
      
      const nodePool = generateSolvableNodePool([startNode], [endNode], 3);
      
      expect(nodePool).toHaveLength(3);
      expect(nodePool.every(node => node.type === NodeType.PROCESS)).toBe(true);
    });
  });

  describe('checkGameCompletion', () => {
    it('should detect incomplete games', () => {
      const nodes: GameNode[] = [
        {
          id: '1',
          type: NodeType.START,
          position: { x: 0, y: 0 },
          inputPorts: [],
          outputPorts: [{
            id: 'p1',
            shape: PortShape.SQUARE,
            color: PortColor.RED,
            direction: PortDirection.OUTPUT,
            nodeId: '1',
            position: { x: 0, y: 0 },
            isConnected: false
          }]
        }
      ];
      
      const completion = checkGameCompletion(nodes, []);
      
      expect(completion.isComplete).toBe(false);
      expect(completion.isValid).toBe(false);
      expect(completion.errors.length).toBeGreaterThan(0);
    });
  });
});
