import { useState, useCallback, useEffect } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { GameMode } from './types';
import type {
  GameState,
  GameNode,
  Connection as GameConnection
} from './types';
import {
  isGameSolvable,
  areAllPortsConnected,
  calculateNodeDepths,
  checkGameCompletion,
  calculateScore,
  generateNextLevel,
  shouldDropNewNode,
  calculateDropInterval,
  validateGameState
} from './utils/gameLogic';
import {
  createStartNode,
  createEndNode,
  generateCompatibleNode,
  generateSolvableNodePool
} from './utils/nodeGenerator';
import { intelligentNodeGenerator } from './utils/intelligentNodeGenerator';
import GameCanvas from './components/GameCanvas';
import TemporaryNodeArea from './components/TemporaryNodeArea';
import GameControls from './components/GameControls';
import './App.css';

function App() {
  const [gameState, setGameState] = useState<GameState>({
    nodes: [],
    connections: [],
    temporaryNodes: [],
    isGameRunning: false,
    score: 0,
    level: 1
  });

  const [gameMode, setGameMode] = useState<GameMode>(GameMode.TURN_BASED);
  const [selectedTempNodeId, setSelectedTempNodeId] = useState<string | null>(null);
  const [isAnimationPlaying, setIsAnimationPlaying] = useState(false);
  const [lastDropTime, setLastDropTime] = useState<number>(Date.now());
  const [gameStartTime, setGameStartTime] = useState<number>(Date.now());
  const [gameCompletion, setGameCompletion] = useState<{
    isComplete: boolean;
    isValid: boolean;
    errors: string[];
  }>({ isComplete: false, isValid: false, errors: [] });
  const [currentSolution, setCurrentSolution] = useState<{ fromPortId: string; toPortId: string }[]>([]);
  const [allowLineCrossing, setAllowLineCrossing] = useState<boolean>(true);

  // 初始化游戏
  const initializeGame = useCallback(() => {
    const levelConfig = generateNextLevel(1);

    // 使用智能节点生成器创建保证可解的关卡
    const levelData = intelligentNodeGenerator.generateSolvableLevel({
      startNodeCount: levelConfig.startNodeCount,
      endNodeCount: levelConfig.endNodeCount,
      maxTempNodes: levelConfig.maxTempNodes,
      allowLineCrossing: gameMode === GameMode.TETRIS // 俄罗斯方块模式允许线条相交
    });

    setGameState({
      nodes: [...levelData.startNodes, ...levelData.endNodes],
      connections: [],
      temporaryNodes: levelData.processNodes.slice(0, 3), // 初始显示3个节点
      isGameRunning: false,
      score: 0,
      level: 1
    });

    setCurrentSolution(levelData.solution);
    setSelectedTempNodeId(null);
    setGameStartTime(Date.now());
    setLastDropTime(Date.now());
    setGameCompletion({ isComplete: false, isValid: false, errors: [] });
    setAllowLineCrossing(gameMode === GameMode.TETRIS);
  }, [gameMode]);

  // 初始化游戏
  useEffect(() => {
    initializeGame();
  }, [initializeGame]);



  // 生成新节点
  const generateNewNode = useCallback(() => {
    const levelConfig = generateNextLevel(gameState.level);
    if (gameState.temporaryNodes.length >= levelConfig.maxTempNodes) return;

    const newNode = generateCompatibleNode(gameState.nodes);
    setGameState(prev => ({
      ...prev,
      temporaryNodes: [...prev.temporaryNodes, newNode]
    }));
  }, [gameState.nodes, gameState.temporaryNodes.length, gameState.level]);

  // 移动节点
  const handleNodeMove = useCallback((nodeId: string, newPosition: { x: number; y: number }) => {
    setGameState(prev => ({
      ...prev,
      nodes: prev.nodes.map(node =>
        node.id === nodeId
          ? {
              ...node,
              position: newPosition,
              inputPorts: node.inputPorts.map(port => ({
                ...port,
                position: { x: 0, y: port.position.y }
              })),
              outputPorts: node.outputPorts.map(port => ({
                ...port,
                position: { x: 120, y: port.position.y }
              }))
            }
          : node
      )
    }));
  }, []);

  // 创建连接
  const handleConnectionCreate = useCallback((fromPortId: string, toPortId: string) => {
    const fromPort = gameState.nodes
      .flatMap(node => [...node.inputPorts, ...node.outputPorts])
      .find(port => port.id === fromPortId);

    const toPort = gameState.nodes
      .flatMap(node => [...node.inputPorts, ...node.outputPorts])
      .find(port => port.id === toPortId);

    if (!fromPort || !toPort) return;

    const newConnection: GameConnection = {
      id: uuidv4(),
      fromPortId,
      toPortId,
      fromNodeId: fromPort.nodeId,
      toNodeId: toPort.nodeId
    };

    setGameState(prev => ({
      ...prev,
      connections: [...prev.connections, newConnection],
      nodes: prev.nodes.map(node => ({
        ...node,
        inputPorts: node.inputPorts.map(port =>
          port.id === fromPortId || port.id === toPortId
            ? { ...port, isConnected: true }
            : port
        ),
        outputPorts: node.outputPorts.map(port =>
          port.id === fromPortId || port.id === toPortId
            ? { ...port, isConnected: true }
            : port
        )
      }))
    }));
  }, [gameState.nodes]);

  // 删除连接
  const handleConnectionDelete = useCallback((connectionId: string) => {
    const connection = gameState.connections.find(c => c.id === connectionId);
    if (!connection) return;

    setGameState(prev => ({
      ...prev,
      connections: prev.connections.filter(c => c.id !== connectionId),
      nodes: prev.nodes.map(node => ({
        ...node,
        inputPorts: node.inputPorts.map(port =>
          port.id === connection.fromPortId || port.id === connection.toPortId
            ? { ...port, isConnected: false }
            : port
        ),
        outputPorts: node.outputPorts.map(port =>
          port.id === connection.fromPortId || port.id === connection.toPortId
            ? { ...port, isConnected: false }
            : port
        )
      }))
    }));
  }, [gameState.connections]);

  // 从临时区域添加节点到游戏区域
  const handleAddNodeFromTemp = useCallback((tempNode: GameNode, position: { x: number; y: number }) => {
    const newNode: GameNode = {
      ...tempNode,
      position,
      inputPorts: tempNode.inputPorts.map(port => ({
        ...port,
        position: { x: 0, y: port.position.y }
      })),
      outputPorts: tempNode.outputPorts.map(port => ({
        ...port,
        position: { x: 120, y: port.position.y }
      }))
    };

    setGameState(prev => ({
      ...prev,
      nodes: [...prev.nodes, newNode],
      temporaryNodes: prev.temporaryNodes.filter(node => node.id !== tempNode.id)
    }));
    setSelectedTempNodeId(null);
  }, []);

  // 检查游戏完成状态
  const checkCompletion = useCallback(() => {
    const validation = validateGameState(
      gameState.nodes,
      gameState.connections,
      allowLineCrossing
    );

    setGameCompletion({
      isComplete: validation.isComplete,
      isValid: validation.isValid,
      errors: validation.errors.map(e => e.message)
    });

    if (validation.isComplete) {
      // 计算得分
      const timeBonus = Math.max(0, 60 - Math.floor((Date.now() - gameStartTime) / 1000)) * 5;
      const score = calculateScore(gameState.nodes, gameState.connections, gameState.level, timeBonus);

      setGameState(prev => ({
        ...prev,
        score: prev.score + score,
        isGameRunning: false
      }));

      // 延迟进入下一关
      setTimeout(() => {
        proceedToNextLevel();
      }, 2000);
    }
  }, [gameState.nodes, gameState.connections, gameState.level, gameStartTime, allowLineCrossing]);

  // 进入下一关
  const proceedToNextLevel = useCallback(() => {
    const nextLevel = gameState.level + 1;
    const levelConfig = generateNextLevel(nextLevel);

    const startNodes: GameNode[] = [];
    const endNodes: GameNode[] = [];

    // 创建起点节点
    for (let i = 0; i < levelConfig.startNodeCount; i++) {
      startNodes.push(createStartNode({
        x: 50,
        y: 150 + i * 100
      }));
    }

    // 创建终点节点
    for (let i = 0; i < levelConfig.endNodeCount; i++) {
      endNodes.push(createEndNode({
        x: 700,
        y: 150 + i * 100
      }));
    }

    // 生成新的临时节点池
    const newTempNodes = generateSolvableNodePool(
      startNodes,
      endNodes,
      Math.min(levelConfig.maxTempNodes, 3)
    );

    setGameState(prev => ({
      ...prev,
      nodes: [...startNodes, ...endNodes],
      connections: [],
      temporaryNodes: newTempNodes,
      level: nextLevel,
      isGameRunning: true
    }));

    setGameStartTime(Date.now());
    setLastDropTime(Date.now());
    setGameCompletion({ isComplete: false, isValid: false, errors: [] });
  }, [gameState.level, gameState.score]);

  // 俄罗斯方块模式的自动掉落逻辑
  useEffect(() => {
    if (gameMode !== GameMode.TETRIS || !gameState.isGameRunning) return;

    const interval = setInterval(() => {
      const levelConfig = generateNextLevel(gameState.level);
      const dropInterval = calculateDropInterval(gameState.level);

      if (shouldDropNewNode(
        gameState.temporaryNodes.length,
        levelConfig.maxTempNodes,
        dropInterval,
        lastDropTime
      )) {
        generateNewNode();
        setLastDropTime(Date.now());
      }
    }, 1000); // 每秒检查一次

    return () => clearInterval(interval);
  }, [gameMode, gameState.isGameRunning, gameState.temporaryNodes.length, gameState.level, lastDropTime, generateNewNode]);

  // 检查游戏完成状态
  useEffect(() => {
    if (gameState.isGameRunning) {
      checkCompletion();
    }
  }, [gameState.connections, checkCompletion, gameState.isGameRunning]);

  return (
    <div className={`app ${gameMode === GameMode.TETRIS ? 'tetris-mode' : 'turn-based-mode'}`}>
      <div className="game-layout">
        <div className="left-panel">
          <GameControls
            gameState={gameState}
            gameMode={gameMode}
            gameCompletion={gameCompletion}
            onGameModeChange={setGameMode}
            onStartGame={() => {
              setGameState(prev => ({ ...prev, isGameRunning: true }));
              setGameStartTime(Date.now());
            }}
            onPauseGame={() => setGameState(prev => ({ ...prev, isGameRunning: false }))}
            onResetGame={initializeGame}
            onGenerateNode={generateNewNode}
            onTestSolution={() => {
              const depths = calculateNodeDepths(gameState.nodes, gameState.connections);
              setGameState(prev => ({
                ...prev,
                nodes: prev.nodes.map(node => ({
                  ...node,
                  depth: depths.get(node.id)
                }))
              }));
              setIsAnimationPlaying(true);
            }}
            canGenerateNode={(() => {
              const levelConfig = generateNextLevel(gameState.level);
              return gameState.temporaryNodes.length < levelConfig.maxTempNodes;
            })()}
            isSolvable={isGameSolvable(gameState.nodes, gameState.connections)}
            allPortsConnected={areAllPortsConnected(gameState.nodes)}
          />
        </div>

        <div className="center-panel">
          <GameCanvas
            nodes={gameState.nodes}
            connections={gameState.connections}
            onNodeMove={handleNodeMove}
            onConnectionCreate={handleConnectionCreate}
            onConnectionDelete={handleConnectionDelete}
            onNodeAdd={handleAddNodeFromTemp}
            selectedTempNode={gameState.temporaryNodes.find(node => node.id === selectedTempNodeId) || null}
            isAnimationPlaying={isAnimationPlaying}
            onAnimationComplete={() => setIsAnimationPlaying(false)}
            width={800}
            height={600}
            allowLineCrossing={allowLineCrossing}
            showPortHighlights={true}
          />
        </div>

        <div className="right-panel">
          <TemporaryNodeArea
            temporaryNodes={gameState.temporaryNodes}
            onNodeSelect={(node) => setSelectedTempNodeId(node.id)}
            selectedNodeId={selectedTempNodeId}
          />
        </div>
      </div>
    </div>
  );
}

export default App;
