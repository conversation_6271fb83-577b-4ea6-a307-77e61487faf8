import './App.css';

function App() {
  return (
    <div className="app">
      <div className="game-layout">
        <div className="left-panel">
          <div className="game-controls">
            <h2>蓝图连接游戏</h2>
            <div className="score-info">
              <p>分数: 0</p>
              <p>等级: 1</p>
            </div>
            
            <div className="game-mode-selector">
              <h3>游戏模式</h3>
              <div className="mode-buttons">
                <button className="active">回合制模式</button>
                <button>俄罗斯方块模式</button>
              </div>
            </div>
            
            <div className="game-actions">
              <h3>游戏控制</h3>
              <div className="action-buttons">
                <button className="start-button">开始游戏</button>
                <button className="reset-button">重置游戏</button>
                <button className="generate-button">生成新节点</button>
                <button className="test-button">测试解决方案</button>
              </div>
            </div>
            
            <div className="game-status">
              <h3>游戏状态</h3>
              <div className="status-indicators">
                <div className="status-item valid">
                  <span className="status-icon">✓</span>
                  <span>图结构有效</span>
                </div>
                <div className="status-item invalid">
                  <span className="status-icon">✗</span>
                  <span>所有端口已连接</span>
                </div>
                <div className="status-item invalid">
                  <span className="status-icon">✗</span>
                  <span>可解</span>
                </div>
              </div>
            </div>
            
            <div className="game-stats">
              <h3>统计信息</h3>
              <div className="stats-grid">
                <div className="stat-item">
                  <span className="stat-label">节点数量:</span>
                  <span className="stat-value">2</span>
                </div>
                <div className="stat-item">
                  <span className="stat-label">连接数量:</span>
                  <span className="stat-value">0</span>
                </div>
                <div className="stat-item">
                  <span className="stat-label">临时节点:</span>
                  <span className="stat-value">2</span>
                </div>
              </div>
            </div>
            
            <div className="game-instructions">
              <h3>游戏说明</h3>
              <ul>
                <li>拖拽节点到游戏区域进行放置</li>
                <li>从输出端口拖拽到输入端口创建连接</li>
                <li>相同形状和颜色的端口才能连接</li>
                <li>点击连接线可以删除连接</li>
                <li>目标：连接所有节点形成有效的数据流</li>
              </ul>
            </div>
          </div>
        </div>
        
        <div className="center-panel">
          <svg width="800" height="600" style={{ border: '2px solid #ccc', backgroundColor: '#f5f5f5' }}>
            {/* 网格背景 */}
            <defs>
              <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
                <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#ddd" strokeWidth="1"/>
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#grid)" />
            
            {/* 起点节点 */}
            <g transform="translate(50, 200)">
              <rect x="0" y="0" width="120" height="90" fill="#4CAF50" stroke="#000" strokeWidth="2" rx="8" ry="8" />
              <text x="60" y="45" textAnchor="middle" dominantBaseline="middle" fill="white" fontSize="12" fontWeight="bold">
                START
              </text>
              {/* 输出端口 */}
              <rect x="115" y="15" width="16" height="16" fill="#ff4444" stroke="#000" strokeWidth="1" transform="translate(-8, -8)" />
              <rect x="115" y="45" width="16" height="16" fill="#4444ff" stroke="#000" strokeWidth="1" transform="translate(-8, -8)" />
            </g>
            
            {/* 终点节点 */}
            <g transform="translate(700, 200)">
              <rect x="0" y="0" width="120" height="90" fill="#F44336" stroke="#000" strokeWidth="2" rx="8" ry="8" />
              <text x="60" y="45" textAnchor="middle" dominantBaseline="middle" fill="white" fontSize="12" fontWeight="bold">
                END
              </text>
              {/* 输入端口 */}
              <rect x="5" y="15" width="16" height="16" fill="#ff4444" stroke="#000" strokeWidth="1" transform="translate(-8, -8)" />
              <rect x="5" y="45" width="16" height="16" fill="#4444ff" stroke="#000" strokeWidth="1" transform="translate(-8, -8)" />
            </g>
            
            {/* 提示文本 */}
            <text x="400" y="100" textAnchor="middle" fill="#666" fontSize="16">
              蓝图连接游戏 - 简化版本
            </text>
            <text x="400" y="130" textAnchor="middle" fill="#666" fontSize="14">
              从左侧起点节点连接到右侧终点节点
            </text>
          </svg>
        </div>
        
        <div className="right-panel">
          <div className="temporary-node-area">
            <h3>临时节点区域</h3>
            <div className="temporary-nodes-container">
              <svg width="400" height="200" style={{ border: '2px solid #999', backgroundColor: '#e8e8e8' }}>
                <defs>
                  <pattern id="temp-grid" width="20" height="20" patternUnits="userSpaceOnUse">
                    <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#ccc" strokeWidth="1"/>
                  </pattern>
                </defs>
                <rect width="100%" height="100%" fill="url(#temp-grid)" />
                
                {/* 临时节点1 */}
                <g transform="translate(20, 50)">
                  <rect x="0" y="0" width="120" height="90" fill="#2196F3" stroke="#000" strokeWidth="2" rx="8" ry="8" />
                  <text x="60" y="45" textAnchor="middle" dominantBaseline="middle" fill="white" fontSize="12" fontWeight="bold">
                    PROCESS
                  </text>
                  {/* 端口 */}
                  <rect x="5" y="25" width="16" height="16" fill="#ff4444" stroke="#000" strokeWidth="1" transform="translate(-8, -8)" />
                  <rect x="115" y="25" width="16" height="16" fill="#44ff44" stroke="#000" strokeWidth="1" transform="translate(-8, -8)" />
                </g>
                
                {/* 临时节点2 */}
                <g transform="translate(160, 50)">
                  <rect x="0" y="0" width="120" height="90" fill="#2196F3" stroke="#000" strokeWidth="2" rx="8" ry="8" />
                  <text x="60" y="45" textAnchor="middle" dominantBaseline="middle" fill="white" fontSize="12" fontWeight="bold">
                    PROCESS
                  </text>
                  {/* 端口 */}
                  <circle cx="5" cy="25" r="8" fill="#4444ff" stroke="#000" strokeWidth="1" />
                  <polygon points="115,-8 123,0 115,8 107,0" fill="#ffff44" stroke="#000" strokeWidth="1" transform="translate(0, 25)" />
                </g>
              </svg>
            </div>
            
            <div className="temporary-area-info">
              <p>节点数量: 2/3</p>
              <p>点击节点选择，然后拖拽到游戏区域</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default App;
