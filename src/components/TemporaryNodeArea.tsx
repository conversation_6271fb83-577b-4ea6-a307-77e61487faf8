import React from 'react';
import type { GameNode as GameNodeType } from '../types';
import GameNode from './GameNode';

interface TemporaryNodeAreaProps {
  temporaryNodes: GameNodeType[];
  onNodeSelect: (node: GameNodeType) => void;
  selectedNodeId?: string | null;
}

const TemporaryNodeArea: React.FC<TemporaryNodeAreaProps> = ({
  temporaryNodes,
  onNodeSelect,
  selectedNodeId
}) => {
  const handleNodeClick = (node: GameNodeType) => {
    onNodeSelect(node);
  };

  return (
    <div className="temporary-node-area">
      <h3>临时节点区域</h3>
      <div className="temporary-nodes-container">
        <svg width="400" height="200" style={{ border: '2px solid #999', backgroundColor: '#e8e8e8' }}>
          {/* 背景网格 */}
          <defs>
            <pattern id="temp-grid" width="20" height="20" patternUnits="userSpaceOnUse">
              <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#ccc" strokeWidth="1"/>
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#temp-grid)" />
          
          {/* 临时节点 */}
          {temporaryNodes.map((node, index) => (
            <g key={node.id}>
              {/* 选中高亮 */}
              {selectedNodeId === node.id && (
                <rect
                  x={10 + index * 140 - 5}
                  y={45}
                  width={130}
                  height={Math.max(60, Math.max(node.inputPorts.length, node.outputPorts.length) * 30 + 20) + 10}
                  fill="none"
                  stroke="#ffeb3b"
                  strokeWidth="3"
                  rx="10"
                  ry="10"
                />
              )}
              
              <g
                transform={`translate(${10 + index * 140}, 50)`}
                style={{ cursor: 'pointer' }}
                onClick={() => handleNodeClick(node)}
              >
                <GameNode
                  node={{
                    ...node,
                    position: { x: 0, y: 0 }
                  }}
                  onPortClick={() => {}} // 临时区域的端口不可点击
                  onPortMouseDown={() => {}} // 临时区域的端口不可拖拽
                  onNodeMouseDown={() => {}} // 临时区域的节点不可拖拽
                />
              </g>
            </g>
          ))}
          
          {/* 空状态提示 */}
          {temporaryNodes.length === 0 && (
            <text
              x="200"
              y="100"
              textAnchor="middle"
              dominantBaseline="middle"
              fill="#666"
              fontSize="14"
            >
              等待新节点生成...
            </text>
          )}
        </svg>
      </div>
      
      <div className="temporary-area-info">
        <p>节点数量: {temporaryNodes.length}/3</p>
        <p>点击节点选择，然后拖拽到游戏区域</p>
      </div>
    </div>
  );
};

export default TemporaryNodeArea;
