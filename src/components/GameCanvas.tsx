import React, { useState, useRef, useCallback } from 'react';
import { PortDirection } from '../types';
import type {
  GameNode as GameNodeType,
  Port,
  Connection as GameConnection,
  ConnectionPreview,
  DragState
} from '../types';
import GameNode from './GameNode';
import Connection, { TempConnection } from './Connection';
import DataFlowAnimation from './DataFlowAnimation';
import {
  canConnectPorts,
  validateConnectionPreview,
  getCompatiblePorts,
  checkConnectionCrossing
} from '../utils/gameLogic';

interface GameCanvasProps {
  nodes: GameNodeType[];
  connections: GameConnection[];
  onNodeMove: (nodeId: string, newPosition: { x: number; y: number }) => void;
  onConnectionCreate: (fromPortId: string, toPortId: string) => void;
  onConnectionDelete: (connectionId: string) => void;
  onNodeAdd?: (node: GameNodeType, position: { x: number; y: number }) => void;
  selectedTempNode?: GameNodeType | null;
  isAnimationPlaying?: boolean;
  onAnimationComplete?: () => void;
  width: number;
  height: number;
  allowLineCrossing?: boolean;
  showPortHighlights?: boolean;
}

const GameCanvas: React.FC<GameCanvasProps> = ({
  nodes,
  connections,
  onNodeMove,
  onConnectionCreate,
  onConnectionDelete,
  onNodeAdd,
  selectedTempNode,
  isAnimationPlaying = false,
  onAnimationComplete,
  width,
  height,
  allowLineCrossing = true,
  showPortHighlights = true
}) => {
  const [draggedNode, setDraggedNode] = useState<GameNodeType | null>(null);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [connectionDrag, setConnectionDrag] = useState<{
    fromPort: Port;
    currentPosition: { x: number; y: number };
  } | null>(null);
  const [highlightedPortId, setHighlightedPortId] = useState<string | null>(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  const svgRef = useRef<SVGSVGElement>(null);

  // 获取SVG坐标
  const getSVGCoordinates = useCallback((clientX: number, clientY: number) => {
    if (!svgRef.current) return { x: clientX, y: clientY };
    
    const rect = svgRef.current.getBoundingClientRect();
    return {
      x: clientX - rect.left,
      y: clientY - rect.top
    };
  }, []);

  // 查找端口
  const findPort = useCallback((portId: string): Port | null => {
    for (const node of nodes) {
      const port = [...node.inputPorts, ...node.outputPorts].find(p => p.id === portId);
      if (port) return port;
    }
    return null;
  }, [nodes]);

  // 节点拖拽开始
  const handleNodeMouseDown = useCallback((node: GameNodeType, event: React.MouseEvent) => {
    event.preventDefault();
    const svgCoords = getSVGCoordinates(event.clientX, event.clientY);
    
    setDraggedNode(node);
    setDragOffset({
      x: svgCoords.x - node.position.x,
      y: svgCoords.y - node.position.y
    });
  }, [getSVGCoordinates]);

  // 端口拖拽开始
  const handlePortMouseDown = useCallback((port: Port, event: React.MouseEvent) => {
    event.preventDefault();
    event.stopPropagation();
    
    if (port.direction === PortDirection.OUTPUT) {
      const svgCoords = getSVGCoordinates(event.clientX, event.clientY);
      setConnectionDrag({
        fromPort: port,
        currentPosition: svgCoords
      });
    }
  }, [getSVGCoordinates]);

  // 端口点击
  const handlePortClick = useCallback((port: Port) => {
    if (connectionDrag && connectionDrag.fromPort.id !== port.id) {
      // 检查基本连接规则
      if (canConnectPorts(connectionDrag.fromPort, port)) {
        // 检查线条相交（如果不允许相交）
        if (!allowLineCrossing) {
          const wouldCross = checkConnectionCrossing(
            { fromPort: connectionDrag.fromPort, toPort: port },
            connections,
            nodes
          );

          if (wouldCross) {
            // 可以在这里显示错误提示
            console.warn('Connection would cross existing lines');
            setConnectionDrag(null);
            setHighlightedPortId(null);
            return;
          }
        }

        onConnectionCreate(connectionDrag.fromPort.id, port.id);
      }
      setConnectionDrag(null);
      setHighlightedPortId(null);
    }
  }, [connectionDrag, onConnectionCreate, allowLineCrossing, connections, nodes]);

  // 鼠标移动
  const handleMouseMove = useCallback((event: React.MouseEvent) => {
    const svgCoords = getSVGCoordinates(event.clientX, event.clientY);
    setMousePosition(svgCoords);

    if (draggedNode) {
      // 节点拖拽
      const newPosition = {
        x: svgCoords.x - dragOffset.x,
        y: svgCoords.y - dragOffset.y
      };
      onNodeMove(draggedNode.id, newPosition);
    } else if (connectionDrag) {
      // 连接线拖拽
      setConnectionDrag({
        ...connectionDrag,
        currentPosition: svgCoords
      });
      
      // 使用增强的连接预览验证
      const preview = validateConnectionPreview(
        connectionDrag.fromPort,
        svgCoords,
        nodes
      );

      if (preview.targetPort && preview.isValid) {
        // 如果不允许线条相交，还需要检查相交
        if (!allowLineCrossing) {
          const wouldCross = checkConnectionCrossing(
            { fromPort: connectionDrag.fromPort, toPort: preview.targetPort },
            connections,
            nodes
          );
          setHighlightedPortId(wouldCross ? null : preview.targetPort.id);
        } else {
          setHighlightedPortId(preview.targetPort.id);
        }
      } else {
        setHighlightedPortId(null);
      }
    }
  }, [draggedNode, connectionDrag, dragOffset, getSVGCoordinates, onNodeMove, nodes]);

  // 鼠标释放
  const handleMouseUp = useCallback(() => {
    setDraggedNode(null);
    setConnectionDrag(null);
    setHighlightedPortId(null);
  }, []);

  // 连接线点击
  const handleConnectionClick = useCallback((connection: GameConnection) => {
    onConnectionDelete(connection.id);
  }, [onConnectionDelete]);

  // 画布点击（用于放置选中的临时节点）
  const handleCanvasClick = useCallback((event: React.MouseEvent) => {
    if (selectedTempNode && onNodeAdd) {
      const svgCoords = getSVGCoordinates(event.clientX, event.clientY);
      // 确保节点不会放置在画布边界外
      const clampedPosition = {
        x: Math.max(10, Math.min(svgCoords.x - 60, width - 130)),
        y: Math.max(10, Math.min(svgCoords.y - 30, height - 100))
      };
      onNodeAdd(selectedTempNode, clampedPosition);
    }
  }, [selectedTempNode, onNodeAdd, getSVGCoordinates, width, height]);

  return (
    <svg
      ref={svgRef}
      width={width}
      height={height}
      style={{
        border: '2px solid #ccc',
        backgroundColor: '#f5f5f5',
        cursor: selectedTempNode ? 'crosshair' : 'default'
      }}
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
      onMouseLeave={handleMouseUp}
      onClick={handleCanvasClick}
    >
      {/* 网格背景 */}
      <defs>
        <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
          <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#ddd" strokeWidth="1"/>
        </pattern>
      </defs>
      <rect width="100%" height="100%" fill="url(#grid)" />
      
      {/* 连接线 */}
      {connections.map(connection => {
        const fromPort = findPort(connection.fromPortId);
        const toPort = findPort(connection.toPortId);
        
        if (!fromPort || !toPort) return null;
        
        const fromNode = nodes.find(n => n.id === connection.fromNodeId);
        const toNode = nodes.find(n => n.id === connection.toNodeId);
        
        if (!fromNode || !toNode) return null;
        
        return (
          <Connection
            key={connection.id}
            connection={connection}
            fromPort={{
              ...fromPort,
              position: {
                x: fromNode.position.x + fromPort.position.x,
                y: fromNode.position.y + fromPort.position.y
              }
            }}
            toPort={{
              ...toPort,
              position: {
                x: toNode.position.x + toPort.position.x,
                y: toNode.position.y + toPort.position.y
              }
            }}
            onClick={handleConnectionClick}
          />
        );
      })}
      
      {/* 临时连接线 */}
      {connectionDrag && (() => {
        const preview = validateConnectionPreview(
          connectionDrag.fromPort,
          connectionDrag.currentPosition,
          nodes
        );

        // 检查线条相交（如果不允许相交）
        let isValid = preview.isValid;
        if (isValid && !allowLineCrossing && preview.targetPort) {
          const wouldCross = checkConnectionCrossing(
            { fromPort: connectionDrag.fromPort, toPort: preview.targetPort },
            connections,
            nodes
          );
          isValid = !wouldCross;
        }

        return (
          <TempConnection
            fromPort={{
              ...connectionDrag.fromPort,
              position: {
                x: nodes.find(n => n.id === connectionDrag.fromPort.nodeId)!.position.x + connectionDrag.fromPort.position.x,
                y: nodes.find(n => n.id === connectionDrag.fromPort.nodeId)!.position.y + connectionDrag.fromPort.position.y
              }
            }}
            toPosition={connectionDrag.currentPosition}
            isValid={isValid}
            targetPort={preview.targetPort}
          />
        );
      })()}
      
      {/* 节点 */}
      {nodes.map(node => (
        <GameNode
          key={node.id}
          node={node}
          onPortClick={handlePortClick}
          onPortMouseDown={handlePortMouseDown}
          onNodeMouseDown={handleNodeMouseDown}
          isDragging={draggedNode?.id === node.id}
          highlightedPortId={highlightedPortId}
        />
      ))}

      {/* 临时节点预览 */}
      {selectedTempNode && (
        <g opacity={0.7}>
          <GameNode
            node={{
              ...selectedTempNode,
              position: {
                x: Math.max(10, Math.min(mousePosition.x - 60, width - 130)),
                y: Math.max(10, Math.min(mousePosition.y - 30, height - 100))
              }
            }}
            onPortClick={() => {}}
            onPortMouseDown={() => {}}
            onNodeMouseDown={() => {}}
            isDragging={false}
          />
        </g>
      )}

      {/* 数据流动动画 */}
      <DataFlowAnimation
        nodes={nodes}
        connections={connections}
        isPlaying={isAnimationPlaying}
        onAnimationComplete={onAnimationComplete}
      />
    </svg>
  );
};

export default GameCanvas;
