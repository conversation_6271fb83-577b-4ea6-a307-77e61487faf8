import React from 'react';
import type { Connection as GameConnection, Port } from '../types';

interface ConnectionProps {
  connection: GameConnection;
  fromPort: Port;
  toPort: Port;
  onClick?: (connection: GameConnection) => void;
}

const Connection: React.FC<ConnectionProps> = ({
  connection,
  fromPort,
  toPort,
  onClick
}) => {
  // 计算贝塞尔曲线的控制点
  const fromX = fromPort.position.x;
  const fromY = fromPort.position.y;
  const toX = toPort.position.x;
  const toY = toPort.position.y;
  
  // 控制点距离
  const controlDistance = Math.abs(toX - fromX) * 0.5;
  
  // 控制点位置
  const cp1X = fromX + controlDistance;
  const cp1Y = fromY;
  const cp2X = toX - controlDistance;
  const cp2Y = toY;
  
  // 创建路径字符串
  const pathData = `M ${fromX} ${fromY} C ${cp1X} ${cp1Y}, ${cp2X} ${cp2Y}, ${toX} ${toY}`;
  
  return (
    <g>
      {/* 连接线 */}
      <path
        d={pathData}
        stroke={fromPort.color}
        strokeWidth={3}
        fill="none"
        style={{ cursor: onClick ? 'pointer' : 'default' }}
        onClick={() => onClick && onClick(connection)}
      />
      
      {/* 连接线的点击区域（更宽，便于点击） */}
      <path
        d={pathData}
        stroke="transparent"
        strokeWidth={10}
        fill="none"
        style={{ cursor: onClick ? 'pointer' : 'default' }}
        onClick={() => onClick && onClick(connection)}
      />
      
      {/* 箭头指示方向 */}
      <defs>
        <marker
          id={`arrowhead-${connection.id}`}
          markerWidth="10"
          markerHeight="7"
          refX="9"
          refY="3.5"
          orient="auto"
        >
          <polygon
            points="0 0, 10 3.5, 0 7"
            fill={fromPort.color}
          />
        </marker>
      </defs>
      
      <path
        d={pathData}
        stroke={fromPort.color}
        strokeWidth={3}
        fill="none"
        markerEnd={`url(#arrowhead-${connection.id})`}
        style={{ cursor: onClick ? 'pointer' : 'default' }}
        onClick={() => onClick && onClick(connection)}
      />
    </g>
  );
};

// 临时连接线组件（拖拽时显示）
interface TempConnectionProps {
  fromPort: Port;
  toPosition: { x: number; y: number };
  isValid?: boolean;
  targetPort?: Port;
}

export const TempConnection: React.FC<TempConnectionProps> = ({
  fromPort,
  toPosition,
  isValid = false,
  targetPort
}) => {
  const fromX = fromPort.position.x;
  const fromY = fromPort.position.y;
  const toX = toPosition.x;
  const toY = toPosition.y;

  const controlDistance = Math.abs(toX - fromX) * 0.5;
  const cp1X = fromX + controlDistance;
  const cp1Y = fromY;
  const cp2X = toX - controlDistance;
  const cp2Y = toY;

  const pathData = `M ${fromX} ${fromY} C ${cp1X} ${cp1Y}, ${cp2X} ${cp2Y}, ${toX} ${toY}`;

  // 根据有效性选择颜色和样式
  const strokeColor = isValid ? '#4CAF50' : (targetPort ? '#F44336' : fromPort.color);
  const strokeWidth = isValid ? 4 : 3;
  const opacity = isValid ? 0.9 : 0.7;

  return (
    <g>
      <path
        d={pathData}
        stroke={strokeColor}
        strokeWidth={strokeWidth}
        strokeDasharray="5,5"
        fill="none"
        opacity={opacity}
      />
      {/* 如果有目标端口且有效，显示连接点预览 */}
      {targetPort && isValid && (
        <circle
          cx={toX}
          cy={toY}
          r={8}
          fill={strokeColor}
          opacity={0.6}
          strokeWidth={2}
          stroke="#ffffff"
        />
      )}
      {/* 如果无效但有目标端口，显示错误指示 */}
      {targetPort && !isValid && (
        <g>
          <circle
            cx={toX}
            cy={toY}
            r={10}
            fill="none"
            stroke="#F44336"
            strokeWidth={2}
            opacity={0.8}
          />
          <path
            d={`M ${toX - 6} ${toY - 6} L ${toX + 6} ${toY + 6} M ${toX + 6} ${toY - 6} L ${toX - 6} ${toY + 6}`}
            stroke="#F44336"
            strokeWidth={2}
            opacity={0.8}
          />
        </g>
      )}
    </g>
  );
};

export default Connection;
