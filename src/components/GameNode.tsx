import React from 'react';
import { NodeType } from '../types';
import type { GameNode as GameNodeType, Port as PortType } from '../types';
import Port from './Port';

interface GameNodeProps {
  node: GameNodeType;
  onPortClick: (port: PortType) => void;
  onPortMouseDown: (port: PortType, event: React.MouseEvent) => void;
  onNodeMouseDown: (node: GameNodeType, event: React.MouseEvent) => void;
  isDragging?: boolean;
  highlightedPortId?: string | null;
}

const GameNode: React.FC<GameNodeProps> = ({
  node,
  onPortClick,
  onPortMouseDown,
  onNodeMouseDown,
  isDragging = false,
  highlightedPortId
}) => {
  const getNodeColor = () => {
    switch (node.type) {
      case NodeType.START:
        return '#4CAF50'; // 绿色
      case NodeType.END:
        return '#F44336'; // 红色
      case NodeType.PROCESS:
        return '#2196F3'; // 蓝色
      default:
        return '#9E9E9E'; // 灰色
    }
  };

  const getNodeLabel = () => {
    switch (node.type) {
      case NodeType.START:
        return 'START';
      case NodeType.END:
        return 'END';
      case NodeType.PROCESS:
        return 'PROCESS';
      default:
        return 'NODE';
    }
  };

  const nodeWidth = 120;
  const nodeHeight = Math.max(60, Math.max(node.inputPorts.length, node.outputPorts.length) * 30 + 20);

  return (
    <g
      transform={`translate(${node.position.x}, ${node.position.y})`}
      style={{ cursor: isDragging ? 'grabbing' : 'grab' }}
    >
      {/* 节点主体 */}
      <rect
        x={0}
        y={0}
        width={nodeWidth}
        height={nodeHeight}
        fill={getNodeColor()}
        stroke={isDragging ? '#ffffff' : '#000000'}
        strokeWidth={isDragging ? 3 : 2}
        rx={8}
        ry={8}
        onMouseDown={(e) => onNodeMouseDown(node, e)}
      />
      
      {/* 节点标签 */}
      <text
        x={nodeWidth / 2}
        y={nodeHeight / 2}
        textAnchor="middle"
        dominantBaseline="middle"
        fill="white"
        fontSize="12"
        fontWeight="bold"
        pointerEvents="none"
      >
        {getNodeLabel()}
      </text>
      
      {/* 深度显示（如果有） */}
      {node.depth !== undefined && (
        <text
          x={nodeWidth / 2}
          y={nodeHeight / 2 + 15}
          textAnchor="middle"
          dominantBaseline="middle"
          fill="white"
          fontSize="10"
          pointerEvents="none"
        >
          Depth: {node.depth}
        </text>
      )}
      
      {/* 输入端口 */}
      {node.inputPorts.map((port, index) => (
        <Port
          key={port.id}
          port={{
            ...port,
            position: { x: 0, y: 20 + index * 30 }
          }}
          onPortClick={onPortClick}
          onPortMouseDown={onPortMouseDown}
          isHighlighted={highlightedPortId === port.id}
        />
      ))}
      
      {/* 输出端口 */}
      {node.outputPorts.map((port, index) => (
        <Port
          key={port.id}
          port={{
            ...port,
            position: { x: nodeWidth, y: 20 + index * 30 }
          }}
          onPortClick={onPortClick}
          onPortMouseDown={onPortMouseDown}
          isHighlighted={highlightedPortId === port.id}
        />
      ))}
    </g>
  );
};

export default GameNode;
