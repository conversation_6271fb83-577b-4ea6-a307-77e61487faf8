/* 全局样式 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f0f0f0;
}

.app {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 游戏布局 */
.game-layout {
  display: flex;
  height: 100vh;
  gap: 10px;
  padding: 10px;
}

.left-panel {
  width: 300px;
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}

.center-panel {
  flex: 1;
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
}

.right-panel {
  width: 450px;
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* 游戏控制面板样式 */
.game-controls h2 {
  color: #333;
  margin-bottom: 20px;
  text-align: center;
}

.game-controls h3 {
  color: #555;
  margin: 20px 0 10px 0;
  font-size: 16px;
  border-bottom: 2px solid #eee;
  padding-bottom: 5px;
}

.score-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.score-info p {
  margin: 0;
  font-weight: bold;
  color: #333;
}

/* 模式选择器 */
.mode-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.mode-buttons button {
  padding: 10px 15px;
  border: 2px solid #ddd;
  background-color: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.mode-buttons button:hover {
  background-color: #f0f0f0;
}

.mode-buttons button.active {
  background-color: #007bff;
  color: white;
  border-color: #007bff;
}

.mode-buttons button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 动作按钮 */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.action-buttons button {
  padding: 12px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  transition: all 0.3s ease;
}

.start-button {
  background-color: #28a745;
  color: white;
}

.start-button:hover {
  background-color: #218838;
}

.pause-button {
  background-color: #ffc107;
  color: #212529;
}

.pause-button:hover {
  background-color: #e0a800;
}

.reset-button {
  background-color: #dc3545;
  color: white;
}

.reset-button:hover {
  background-color: #c82333;
}

.generate-button {
  background-color: #17a2b8;
  color: white;
}

.generate-button:hover {
  background-color: #138496;
}

.test-button {
  background-color: #6f42c1;
  color: white;
}

.test-button:hover {
  background-color: #5a32a3;
}

.action-buttons button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 状态指示器 */
.status-indicators {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  border-radius: 4px;
  font-size: 14px;
}

.status-item.valid {
  background-color: #d4edda;
  color: #155724;
}

.status-item.invalid {
  background-color: #f8d7da;
  color: #721c24;
}

.status-icon {
  font-weight: bold;
  font-size: 16px;
}

/* 统计信息 */
.stats-grid {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  padding: 8px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.stat-label {
  color: #666;
}

.stat-value {
  font-weight: bold;
  color: #333;
}

/* 游戏说明 */
.game-instructions ul {
  list-style-type: none;
  padding: 0;
}

.game-instructions li {
  padding: 6px 0;
  border-bottom: 1px solid #eee;
  font-size: 13px;
  color: #666;
}

.game-instructions li:last-child {
  border-bottom: none;
}

/* 临时节点区域样式 */
.temporary-node-area h3 {
  color: #333;
  margin-bottom: 15px;
  text-align: center;
  font-size: 18px;
}

.temporary-nodes-container {
  margin-bottom: 15px;
}

.temporary-area-info {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  border: 1px solid #dee2e6;
}

.temporary-area-info p {
  margin: 5px 0;
  font-size: 14px;
  color: #666;
}

.temporary-area-info p:first-child {
  font-weight: bold;
  color: #333;
}

/* 错误消息 */
.error-messages {
  margin-top: 15px;
  padding: 10px;
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 4px;
}

.error-messages h4 {
  margin: 0 0 8px 0;
  color: #856404;
  font-size: 14px;
}

.error-messages ul {
  margin: 0;
  padding-left: 20px;
}

.error-item {
  color: #856404;
  font-size: 13px;
  margin-bottom: 4px;
}

/* 完成消息 */
.completion-message {
  margin-top: 15px;
  padding: 15px;
  background-color: #d4edda;
  border: 1px solid #c3e6cb;
  border-radius: 4px;
  text-align: center;
  animation: fadeIn 0.5s ease-in;
}

.completion-message h4 {
  margin: 0 0 8px 0;
  color: #155724;
  font-size: 16px;
}

.completion-message p {
  margin: 0;
  color: #155724;
  font-size: 14px;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 游戏模式特定样式 */
.tetris-mode .temporary-node-area {
  border: 2px solid #ff6b6b;
  background: linear-gradient(135deg, #fff5f5 0%, #ffe8e8 100%);
}

.tetris-mode .temporary-node-area h3::after {
  content: " (俄罗斯方块模式)";
  font-size: 12px;
  color: #ff6b6b;
  font-weight: normal;
}

.turn-based-mode .temporary-node-area {
  border: 2px solid #4ecdc4;
  background: linear-gradient(135deg, #f0fffe 0%, #e8fffe 100%);
}

.turn-based-mode .temporary-node-area h3::after {
  content: " (回合制模式)";
  font-size: 12px;
  color: #4ecdc4;
  font-weight: normal;
}

/* 游戏特性指示器 */
.game-features {
  margin-top: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.game-features h4 {
  margin: 0 0 12px 0;
  color: #495057;
  font-size: 14px;
}

.feature-indicators {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 8px;
  background: white;
  border-radius: 4px;
  font-size: 12px;
  color: #6c757d;
}

.feature-icon {
  font-size: 14px;
}

/* 连接预览增强 */
.connection-preview-valid {
  stroke: #4CAF50;
  stroke-width: 4px;
  opacity: 0.9;
}

.connection-preview-invalid {
  stroke: #F44336;
  stroke-width: 3px;
  opacity: 0.7;
}

.connection-preview-target {
  fill: #4CAF50;
  opacity: 0.6;
  stroke: #ffffff;
  stroke-width: 2px;
}

.connection-preview-error {
  fill: none;
  stroke: #F44336;
  stroke-width: 2px;
  opacity: 0.8;
}

/* 端口高亮 */
.port-highlighted {
  filter: drop-shadow(0 0 8px #4CAF50);
  transform: scale(1.2);
  transition: all 0.2s ease;
}

.port-compatible {
  filter: drop-shadow(0 0 4px #2196F3);
  opacity: 0.8;
}

.port-incompatible {
  opacity: 0.3;
}

/* 智能节点生成指示器 */
.intelligent-generation-indicator {
  position: absolute;
  top: 10px;
  right: 10px;
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: bold;
  opacity: 0.8;
}

/* 验证错误高亮 */
.node-error {
  filter: drop-shadow(0 0 8px #F44336);
}

.connection-error {
  stroke: #F44336 !important;
  stroke-width: 4px !important;
  stroke-dasharray: 3,3 !important;
}

/* 解决方案提示 */
.solution-hint {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(76, 175, 80, 0.9);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: bold;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.solution-hint.visible {
  opacity: 1;
}

/* 线条相交检测 */
.line-crossing-warning {
  stroke: #FF9800 !important;
  stroke-width: 5px !important;
  stroke-dasharray: 8,4 !important;
  opacity: 0.8 !important;
}
