import { NodeType, PortDirection } from '../types';
import type {
  GameNode,
  Connection,
  Port,
  ValidationResult,
  ValidationError,
  ConnectionPreview
} from '../types';

// 检查两个端口是否可以连接
export function canConnectPorts(fromPort: Port, toPort: Port): boolean {
  // 必须是不同方向的端口
  if (fromPort.direction === toPort.direction) {
    return false;
  }
  
  // 必须是相同形状和颜色
  if (fromPort.shape !== toPort.shape || fromPort.color !== toPort.color) {
    return false;
  }
  
  // 不能连接到同一个节点
  if (fromPort.nodeId === toPort.nodeId) {
    return false;
  }
  
  // 端口不能已经被连接
  if (fromPort.isConnected || toPort.isConnected) {
    return false;
  }
  
  return true;
}

// 检查图是否有环（有向无环图检查）
export function hasCircularDependency(nodes: GameNode[], connections: Connection[]): boolean {
  const visited = new Set<string>();
  const recursionStack = new Set<string>();
  
  // 构建邻接表
  const adjacencyList = new Map<string, string[]>();
  nodes.forEach(node => {
    adjacencyList.set(node.id, []);
  });
  
  connections.forEach(connection => {
    const fromNodeConnections = adjacencyList.get(connection.fromNodeId) || [];
    fromNodeConnections.push(connection.toNodeId);
    adjacencyList.set(connection.fromNodeId, fromNodeConnections);
  });
  
  // DFS检查环
  function dfs(nodeId: string): boolean {
    visited.add(nodeId);
    recursionStack.add(nodeId);
    
    const neighbors = adjacencyList.get(nodeId) || [];
    for (const neighbor of neighbors) {
      if (!visited.has(neighbor)) {
        if (dfs(neighbor)) {
          return true;
        }
      } else if (recursionStack.has(neighbor)) {
        return true; // 发现环
      }
    }
    
    recursionStack.delete(nodeId);
    return false;
  }
  
  // 检查所有节点
  for (const node of nodes) {
    if (!visited.has(node.id)) {
      if (dfs(node.id)) {
        return true;
      }
    }
  }
  
  return false;
}

// 计算节点深度（拓扑排序）
export function calculateNodeDepths(nodes: GameNode[], connections: Connection[]): Map<string, number> {
  const depths = new Map<string, number>();
  const inDegree = new Map<string, number>();
  const adjacencyList = new Map<string, string[]>();
  
  // 初始化
  nodes.forEach(node => {
    inDegree.set(node.id, 0);
    adjacencyList.set(node.id, []);
    if (node.type === NodeType.START) {
      depths.set(node.id, 0);
    }
  });
  
  // 构建图和计算入度
  connections.forEach(connection => {
    const fromConnections = adjacencyList.get(connection.fromNodeId) || [];
    fromConnections.push(connection.toNodeId);
    adjacencyList.set(connection.fromNodeId, fromConnections);
    
    const currentInDegree = inDegree.get(connection.toNodeId) || 0;
    inDegree.set(connection.toNodeId, currentInDegree + 1);
  });
  
  // 拓扑排序
  const queue: string[] = [];
  nodes.forEach(node => {
    if (inDegree.get(node.id) === 0) {
      queue.push(node.id);
      if (!depths.has(node.id)) {
        depths.set(node.id, 0);
      }
    }
  });
  
  while (queue.length > 0) {
    const currentNodeId = queue.shift()!;
    const currentDepth = depths.get(currentNodeId) || 0;
    
    const neighbors = adjacencyList.get(currentNodeId) || [];
    neighbors.forEach(neighborId => {
      const newInDegree = (inDegree.get(neighborId) || 0) - 1;
      inDegree.set(neighborId, newInDegree);
      
      const newDepth = currentDepth + 1;
      const existingDepth = depths.get(neighborId) || 0;
      depths.set(neighborId, Math.max(existingDepth, newDepth));
      
      if (newInDegree === 0) {
        queue.push(neighborId);
      }
    });
  }
  
  return depths;
}

// 检查游戏是否可解
export function isGameSolvable(nodes: GameNode[], connections: Connection[]): boolean {
  // 检查是否有环
  if (hasCircularDependency(nodes, connections)) {
    return false;
  }
  
  // 检查是否有起点和终点
  const hasStart = nodes.some(node => node.type === NodeType.START);
  const hasEnd = nodes.some(node => node.type === NodeType.END);
  
  if (!hasStart || !hasEnd) {
    return false;
  }
  
  // 检查所有节点是否都能连通
  const depths = calculateNodeDepths(nodes, connections);
  const endNodes = nodes.filter(node => node.type === NodeType.END);
  
  // 所有终点节点都应该有深度值（可达）
  return endNodes.every(endNode => depths.has(endNode.id));
}

// 检查所有端口是否都已连接
export function areAllPortsConnected(nodes: GameNode[]): boolean {
  for (const node of nodes) {
    // 起点节点的输入端口可以不连接
    if (node.type !== NodeType.START) {
      if (node.inputPorts.some(port => !port.isConnected)) {
        return false;
      }
    }

    // 终点节点的输出端口可以不连接
    if (node.type !== NodeType.END) {
      if (node.outputPorts.some(port => !port.isConnected)) {
        return false;
      }
    }
  }

  return true;
}

// 计算游戏得分
export function calculateScore(
  nodes: GameNode[],
  connections: Connection[],
  level: number,
  timeBonus: number = 0
): number {
  let score = 0;

  // 基础分数：每个连接10分
  score += connections.length * 10;

  // 节点复杂度奖励：每个处理节点5分
  const processNodes = nodes.filter(node => node.type === NodeType.PROCESS);
  score += processNodes.length * 5;

  // 端口多样性奖励
  const uniquePortTypes = new Set();
  nodes.forEach(node => {
    [...node.inputPorts, ...node.outputPorts].forEach(port => {
      uniquePortTypes.add(`${port.shape}-${port.color}`);
    });
  });
  score += uniquePortTypes.size * 3;

  // 等级乘数
  score *= level;

  // 时间奖励
  score += timeBonus;

  return Math.floor(score);
}

// 增强的游戏验证系统
export function validateGameState(
  nodes: GameNode[],
  connections: Connection[],
  allowLineCrossing: boolean = true
): ValidationResult {
  const errors: ValidationError[] = [];
  const warnings: string[] = [];

  // 检查循环依赖
  if (hasCircularDependency(nodes, connections)) {
    errors.push({
      type: 'CYCLE_DETECTED',
      message: '检测到循环依赖，这会导致无限循环',
      connectionIds: connections.map(c => c.id)
    });
  }

  // 检查未连接的端口
  const unconnectedPorts = getUnconnectedPorts(nodes);
  if (unconnectedPorts.length > 0) {
    errors.push({
      type: 'UNCONNECTED_PORT',
      message: `存在 ${unconnectedPorts.length} 个未连接的端口`,
      portIds: unconnectedPorts.map(p => p.id)
    });
  }

  // 检查不可达的节点
  const unreachableNodes = findUnreachableNodes(nodes, connections);
  if (unreachableNodes.length > 0) {
    errors.push({
      type: 'UNREACHABLE_NODE',
      message: `存在 ${unreachableNodes.length} 个不可达的终点节点`,
      nodeIds: unreachableNodes.map(n => n.id)
    });
  }

  // 检查连接线相交（仅在不允许相交时）
  if (!allowLineCrossing) {
    const crossingConnections = findCrossingConnections(connections, nodes);
    if (crossingConnections.length > 0) {
      errors.push({
        type: 'LINE_CROSSING',
        message: `存在 ${crossingConnections.length} 处连接线相交`,
        connectionIds: crossingConnections
      });
    }
  }

  // 检查无效连接
  const invalidConnections = findInvalidConnections(connections, nodes);
  if (invalidConnections.length > 0) {
    errors.push({
      type: 'INVALID_CONNECTION',
      message: `存在 ${invalidConnections.length} 个无效连接`,
      connectionIds: invalidConnections
    });
  }

  const isValid = errors.length === 0;
  const isComplete = isValid && areAllPortsConnected(nodes);

  return {
    isValid,
    isComplete,
    errors,
    warnings
  };
}

// 获取未连接的端口
function getUnconnectedPorts(nodes: GameNode[]): Port[] {
  const unconnectedPorts: Port[] = [];

  for (const node of nodes) {
    // 起点节点的输入端口可以不连接
    if (node.type !== NodeType.START) {
      unconnectedPorts.push(...node.inputPorts.filter(port => !port.isConnected));
    }

    // 终点节点的输出端口可以不连接
    if (node.type !== NodeType.END) {
      unconnectedPorts.push(...node.outputPorts.filter(port => !port.isConnected));
    }
  }

  return unconnectedPorts;
}

// 查找不可达的终点节点
function findUnreachableNodes(nodes: GameNode[], connections: Connection[]): GameNode[] {
  const depths = calculateNodeDepths(nodes, connections);
  const endNodes = nodes.filter(node => node.type === NodeType.END);

  return endNodes.filter(endNode => !depths.has(endNode.id));
}

// 查找相交的连接
function findCrossingConnections(connections: Connection[], nodes: GameNode[]): string[] {
  const crossingConnections: string[] = [];

  for (let i = 0; i < connections.length; i++) {
    for (let j = i + 1; j < connections.length; j++) {
      const conn1 = connections[i];
      const conn2 = connections[j];

      const fromPort1 = findPortById(conn1.fromPortId, nodes);
      const toPort1 = findPortById(conn1.toPortId, nodes);
      const fromPort2 = findPortById(conn2.fromPortId, nodes);
      const toPort2 = findPortById(conn2.toPortId, nodes);

      if (!fromPort1 || !toPort1 || !fromPort2 || !toPort2) continue;

      if (checkConnectionCrossing(
        { fromPort: fromPort1, toPort: toPort1 },
        [conn2],
        nodes
      )) {
        crossingConnections.push(conn1.id, conn2.id);
      }
    }
  }

  return [...new Set(crossingConnections)];
}

// 查找无效连接
function findInvalidConnections(connections: Connection[], nodes: GameNode[]): string[] {
  const invalidConnections: string[] = [];

  for (const connection of connections) {
    const fromPort = findPortById(connection.fromPortId, nodes);
    const toPort = findPortById(connection.toPortId, nodes);

    if (!fromPort || !toPort || !canConnectPorts(fromPort, toPort)) {
      invalidConnections.push(connection.id);
    }
  }

  return invalidConnections;
}

// 根据ID查找端口
function findPortById(portId: string, nodes: GameNode[]): Port | undefined {
  for (const node of nodes) {
    const port = [...node.inputPorts, ...node.outputPorts].find(p => p.id === portId);
    if (port) return port;
  }
  return undefined;
}

// 兼容性函数（保持向后兼容）
export function checkGameCompletion(nodes: GameNode[], connections: Connection[]): {
  isComplete: boolean;
  isValid: boolean;
  errors: string[];
} {
  const result = validateGameState(nodes, connections);
  return {
    isComplete: result.isComplete,
    isValid: result.isValid,
    errors: result.errors.map(e => e.message)
  };
}

// 生成下一关的配置
export function generateNextLevel(currentLevel: number): {
  startNodeCount: number;
  endNodeCount: number;
  maxTempNodes: number;
  timeLimit?: number;
} {
  const baseConfig = {
    startNodeCount: 1,
    endNodeCount: 1,
    maxTempNodes: 3,
    timeLimit: undefined as number | undefined
  };

  // 根据等级调整难度
  if (currentLevel >= 3) {
    baseConfig.startNodeCount = Math.min(2, Math.floor(currentLevel / 3));
  }

  if (currentLevel >= 5) {
    baseConfig.endNodeCount = Math.min(2, Math.floor(currentLevel / 5));
  }

  if (currentLevel >= 2) {
    baseConfig.maxTempNodes = Math.min(6, 3 + Math.floor(currentLevel / 2));
  }

  // 从第10关开始添加时间限制
  if (currentLevel >= 10) {
    baseConfig.timeLimit = Math.max(60, 180 - currentLevel * 5);
  }

  return baseConfig;
}

// 俄罗斯方块模式的节点掉落逻辑
export function shouldDropNewNode(
  currentNodes: number,
  maxNodes: number,
  dropInterval: number,
  lastDropTime: number
): boolean {
  const now = Date.now();
  return (
    currentNodes < maxNodes &&
    (now - lastDropTime) >= dropInterval
  );
}

// 计算俄罗斯方块模式的掉落间隔
export function calculateDropInterval(level: number): number {
  // 基础间隔5秒，每级减少200ms，最小1秒
  return Math.max(1000, 5000 - (level - 1) * 200);
}

// 检查连接预览的有效性
export function validateConnectionPreview(
  fromPort: Port,
  toPosition: { x: number; y: number },
  nodes: GameNode[]
): ConnectionPreview {
  // 查找目标端口
  let targetPort: Port | undefined;
  let isValid = false;

  for (const node of nodes) {
    const allPorts = [...node.inputPorts, ...node.outputPorts];
    for (const port of allPorts) {
      const portWorldPos = {
        x: node.position.x + port.position.x,
        y: node.position.y + port.position.y
      };

      // 检查鼠标位置是否在端口附近（20像素范围内）
      const distance = Math.sqrt(
        Math.pow(toPosition.x - portWorldPos.x, 2) +
        Math.pow(toPosition.y - portWorldPos.y, 2)
      );

      if (distance <= 20) {
        targetPort = port;
        isValid = canConnectPorts(fromPort, port);
        break;
      }
    }
    if (targetPort) break;
  }

  return {
    fromPort,
    toPosition,
    isValid,
    targetPort
  };
}

// 获取兼容的端口列表
export function getCompatiblePorts(sourcePort: Port, nodes: GameNode[]): Port[] {
  const compatiblePorts: Port[] = [];

  for (const node of nodes) {
    const allPorts = [...node.inputPorts, ...node.outputPorts];
    for (const port of allPorts) {
      if (canConnectPorts(sourcePort, port)) {
        compatiblePorts.push(port);
      }
    }
  }

  return compatiblePorts;
}

// 检查连接线是否相交
export function doLinesIntersect(
  line1: { start: { x: number; y: number }, end: { x: number; y: number } },
  line2: { start: { x: number; y: number }, end: { x: number; y: number } }
): boolean {
  const { start: p1, end: p2 } = line1;
  const { start: p3, end: p4 } = line2;

  const denominator = (p1.x - p2.x) * (p3.y - p4.y) - (p1.y - p2.y) * (p3.x - p4.x);

  if (denominator === 0) {
    return false; // 平行线
  }

  const t = ((p1.x - p3.x) * (p3.y - p4.y) - (p1.y - p3.y) * (p3.x - p4.x)) / denominator;
  const u = -((p1.x - p2.x) * (p1.y - p3.y) - (p1.y - p2.y) * (p1.x - p3.x)) / denominator;

  return t >= 0 && t <= 1 && u >= 0 && u <= 1;
}

// 检查连接是否与现有连接相交
export function checkConnectionCrossing(
  newConnection: { fromPort: Port, toPort: Port },
  existingConnections: Connection[],
  nodes: GameNode[]
): boolean {
  const fromNode = nodes.find(n => n.id === newConnection.fromPort.nodeId);
  const toNode = nodes.find(n => n.id === newConnection.toPort.nodeId);

  if (!fromNode || !toNode) return false;

  const newLine = {
    start: {
      x: fromNode.position.x + newConnection.fromPort.position.x,
      y: fromNode.position.y + newConnection.fromPort.position.y
    },
    end: {
      x: toNode.position.x + newConnection.toPort.position.x,
      y: toNode.position.y + newConnection.toPort.position.y
    }
  };

  for (const connection of existingConnections) {
    const fromPortExisting = nodes
      .flatMap(n => [...n.inputPorts, ...n.outputPorts])
      .find(p => p.id === connection.fromPortId);
    const toPortExisting = nodes
      .flatMap(n => [...n.inputPorts, ...n.outputPorts])
      .find(p => p.id === connection.toPortId);

    if (!fromPortExisting || !toPortExisting) continue;

    const fromNodeExisting = nodes.find(n => n.id === connection.fromNodeId);
    const toNodeExisting = nodes.find(n => n.id === connection.toNodeId);

    if (!fromNodeExisting || !toNodeExisting) continue;

    const existingLine = {
      start: {
        x: fromNodeExisting.position.x + fromPortExisting.position.x,
        y: fromNodeExisting.position.y + fromPortExisting.position.y
      },
      end: {
        x: toNodeExisting.position.x + toPortExisting.position.x,
        y: toNodeExisting.position.y + toPortExisting.position.y
      }
    };

    if (doLinesIntersect(newLine, existingLine)) {
      return true;
    }
  }

  return false;
}
