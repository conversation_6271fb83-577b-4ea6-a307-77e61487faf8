import { v4 as uuidv4 } from 'uuid';
import { NodeType, PortShape, PortColor, PortDirection } from '../types';
import type { GameNode, Port, GameConfig, LevelConfig } from '../types';

// 智能节点生成器 - 保证可解性
export class IntelligentNodeGenerator {
  private portShapes = Object.values(PortShape);
  private portColors = Object.values(PortColor);
  
  // 生成保证可解的完整关卡
  generateSolvableLevel(config: GameConfig): {
    startNodes: GameNode[];
    endNodes: GameNode[];
    processNodes: GameNode[];
    solution: { fromPortId: string; toPortId: string }[];
  } {
    const startNodes: GameNode[] = [];
    const endNodes: GameNode[] = [];

    // 创建指定数量的起点节点
    for (let i = 0; i < config.startNodeCount; i++) {
      startNodes.push(this.createSimpleStartNode(i));
    }

    // 创建指定数量的终点节点
    for (let i = 0; i < config.endNodeCount; i++) {
      endNodes.push(this.createSimpleEndNode(i));
    }

    // 确保端口类型匹配 - 让终点端口匹配起点端口
    endNodes.forEach((endNode, index) => {
      const startNode = startNodes[index % startNodes.length];
      const startPort = startNode.outputPorts[0];
      const endPort = endNode.inputPorts[0];

      endPort.shape = startPort.shape;
      endPort.color = startPort.color;
    });

    // 生成处理节点和解决方案
    const processNodes: GameNode[] = [];
    const solution: { fromPortId: string; toPortId: string }[] = [];

    // 为每个终点节点创建连接路径
    endNodes.forEach((endNode, index) => {
      const startNode = startNodes[index % startNodes.length];
      const startPort = startNode.outputPorts[0];
      const endPort = endNode.inputPorts[0];

      // 50%概率创建桥接节点
      if (Math.random() < 0.5 && processNodes.length < config.maxTempNodes) {
        const bridgeNode = this.createSimpleBridgeNode(startPort, endPort, index);
        processNodes.push(bridgeNode);

        solution.push(
          { fromPortId: startPort.id, toPortId: bridgeNode.inputPorts[0].id },
          { fromPortId: bridgeNode.outputPorts[0].id, toPortId: endPort.id }
        );
      } else {
        // 直接连接
        solution.push({ fromPortId: startPort.id, toPortId: endPort.id });
      }
    });

    return {
      startNodes,
      endNodes,
      processNodes,
      solution
    };
  }
  
  // 生成起点节点
  private generateStartNodes(count: number): GameNode[] {
    const nodes: GameNode[] = [];
    
    for (let i = 0; i < count; i++) {
      const nodeId = uuidv4();
      const outputPorts: Port[] = [];
      
      // 每个起点节点有1-3个输出端口
      const portCount = Math.floor(Math.random() * 3) + 1;
      
      for (let j = 0; j < portCount; j++) {
        outputPorts.push(this.createPort(
          nodeId,
          PortDirection.OUTPUT,
          this.getRandomPortShape(),
          this.getRandomPortColor(),
          { x: 120, y: 20 + j * 30 }
        ));
      }
      
      nodes.push({
        id: nodeId,
        type: NodeType.START,
        position: { x: 50, y: 100 + i * 150 },
        inputPorts: [],
        outputPorts
      });
    }
    
    return nodes;
  }
  
  // 生成终点节点
  private generateEndNodes(count: number): GameNode[] {
    const nodes: GameNode[] = [];
    
    for (let i = 0; i < count; i++) {
      const nodeId = uuidv4();
      const inputPorts: Port[] = [];
      
      // 每个终点节点有1-3个输入端口
      const portCount = Math.floor(Math.random() * 3) + 1;
      
      for (let j = 0; j < portCount; j++) {
        inputPorts.push(this.createPort(
          nodeId,
          PortDirection.INPUT,
          this.getRandomPortShape(),
          this.getRandomPortColor(),
          { x: 0, y: 20 + j * 30 }
        ));
      }
      
      nodes.push({
        id: nodeId,
        type: NodeType.END,
        position: { x: 700, y: 100 + i * 150 },
        inputPorts,
        outputPorts: []
      });
    }
    
    return nodes;
  }
  
  // 分析需要的连接路径 - 确保端口类型匹配
  private analyzeRequiredPaths(
    startNodes: GameNode[],
    endNodes: GameNode[]
  ): Array<{ startPort: Port; endPort: Port; pathId: string }> {
    const paths: Array<{ startPort: Port; endPort: Port; pathId: string }> = [];

    // 收集所有起点输出端口
    const allStartOutputs = startNodes.flatMap(node => node.outputPorts);

    // 为每个终点节点的输入端口创建一条路径
    for (const endNode of endNodes) {
      for (const endPort of endNode.inputPorts) {
        // 查找匹配的起点端口（相同形状和颜色）
        const matchingStartPorts = allStartOutputs.filter(port =>
          port.shape === endPort.shape && port.color === endPort.color
        );

        let startPort: Port;
        if (matchingStartPorts.length > 0) {
          // 使用匹配的端口
          startPort = matchingStartPorts[Math.floor(Math.random() * matchingStartPorts.length)];
        } else {
          // 如果没有匹配的端口，修改终点端口以匹配一个起点端口
          const randomStartPort = allStartOutputs[Math.floor(Math.random() * allStartOutputs.length)];
          endPort.shape = randomStartPort.shape;
          endPort.color = randomStartPort.color;
          startPort = randomStartPort;
        }

        paths.push({
          startPort,
          endPort,
          pathId: uuidv4()
        });
      }
    }

    return paths;
  }
  
  // 为路径生成处理节点
  private generateProcessNodesForPaths(
    paths: Array<{ startPort: Port; endPort: Port; pathId: string }>,
    startNodes: GameNode[],
    endNodes: GameNode[]
  ): { processNodes: GameNode[]; solution: { fromPortId: string; toPortId: string }[] } {
    const processNodes: GameNode[] = [];
    const solution: { fromPortId: string; toPortId: string }[] = [];
    
    for (const path of paths) {
      // 决定是否需要中间节点（70%概率需要）
      const needsIntermediateNode = Math.random() < 0.7;
      
      if (needsIntermediateNode) {
        // 创建桥接节点
        const bridgeNode = this.createBridgeNode(
          path.startPort,
          path.endPort,
          350 + Math.random() * 200, // x位置
          150 + Math.random() * 200  // y位置
        );
        
        processNodes.push(bridgeNode);
        
        // 添加解决方案连接
        solution.push({
          fromPortId: path.startPort.id,
          toPortId: bridgeNode.inputPorts[0].id
        });
        solution.push({
          fromPortId: bridgeNode.outputPorts[0].id,
          toPortId: path.endPort.id
        });
      } else {
        // 直接连接
        solution.push({
          fromPortId: path.startPort.id,
          toPortId: path.endPort.id
        });
      }
    }
    
    return { processNodes, solution };
  }
  
  // 创建桥接节点
  private createBridgeNode(
    sourcePort: Port,
    targetPort: Port,
    x: number,
    y: number
  ): GameNode {
    const nodeId = uuidv4();
    
    // 创建匹配源端口的输入端口
    const inputPort = this.createPort(
      nodeId,
      PortDirection.INPUT,
      sourcePort.shape,
      sourcePort.color,
      { x: 0, y: 20 }
    );
    
    // 创建匹配目标端口的输出端口
    const outputPort = this.createPort(
      nodeId,
      PortDirection.OUTPUT,
      targetPort.shape,
      targetPort.color,
      { x: 120, y: 20 }
    );
    
    // 只包含必要的端口，不添加额外端口以确保可解性
    const inputPorts = [inputPort];
    const outputPorts = [outputPort];
    
    return {
      id: nodeId,
      type: NodeType.PROCESS,
      position: { x, y },
      inputPorts,
      outputPorts
    };
  }
  
  // 创建简单的起点节点
  private createSimpleStartNode(index: number = 0): GameNode {
    const nodeId = uuidv4();
    const outputPort = this.createPort(
      nodeId,
      PortDirection.OUTPUT,
      this.getRandomPortShape(),
      this.getRandomPortColor(),
      { x: 120, y: 20 }
    );

    return {
      id: nodeId,
      type: NodeType.START,
      position: { x: 50, y: 150 + index * 100 },
      inputPorts: [],
      outputPorts: [outputPort]
    };
  }

  // 创建简单的终点节点
  private createSimpleEndNode(index: number = 0): GameNode {
    const nodeId = uuidv4();
    const inputPort = this.createPort(
      nodeId,
      PortDirection.INPUT,
      this.getRandomPortShape(),
      this.getRandomPortColor(),
      { x: 0, y: 20 }
    );

    return {
      id: nodeId,
      type: NodeType.END,
      position: { x: 700, y: 150 + index * 100 },
      inputPorts: [inputPort],
      outputPorts: []
    };
  }

  // 创建简单的桥接节点
  private createSimpleBridgeNode(sourcePort: Port, targetPort: Port, index: number = 0): GameNode {
    const nodeId = uuidv4();

    const inputPort = this.createPort(
      nodeId,
      PortDirection.INPUT,
      sourcePort.shape,
      sourcePort.color,
      { x: 0, y: 20 }
    );

    const outputPort = this.createPort(
      nodeId,
      PortDirection.OUTPUT,
      targetPort.shape,
      targetPort.color,
      { x: 120, y: 20 }
    );

    return {
      id: nodeId,
      type: NodeType.PROCESS,
      position: { x: 350 + index * 50, y: 200 + index * 50 },
      inputPorts: [inputPort],
      outputPorts: [outputPort]
    };
  }
  
  // 辅助方法
  private createPort(
    nodeId: string,
    direction: PortDirection,
    shape: PortShape,
    color: PortColor,
    position: { x: number; y: number }
  ): Port {
    return {
      id: uuidv4(),
      shape,
      color,
      direction,
      nodeId,
      position,
      isConnected: false
    };
  }
  
  private getRandomPortShape(): PortShape {
    return this.portShapes[Math.floor(Math.random() * this.portShapes.length)];
  }
  
  private getRandomPortColor(): PortColor {
    return this.portColors[Math.floor(Math.random() * this.portColors.length)];
  }
}

// 导出单例实例
export const intelligentNodeGenerator = new IntelligentNodeGenerator();
