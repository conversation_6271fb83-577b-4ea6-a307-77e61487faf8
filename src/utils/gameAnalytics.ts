import type { GameNode, Connection, GameState } from '../types';

// 游戏统计数据接口
export interface GameStatistics {
  totalPlayTime: number;
  levelsCompleted: number;
  totalConnections: number;
  averageCompletionTime: number;
  bestScore: number;
  totalScore: number;
  perfectLevels: number; // 无错误完成的关卡数
  efficiency: number; // 完成效率 (0-100)
  nodeTypesUsed: Record<string, number>;
  portTypesConnected: Record<string, number>;
  mistakeCount: number;
  hintsUsed: number;
}

// 关卡性能分析
export interface LevelPerformance {
  levelNumber: number;
  completionTime: number;
  score: number;
  mistakes: number;
  hintsUsed: number;
  efficiency: number;
  complexity: number;
  timestamp: number;
}

// 游戏分析器类
export class GameAnalytics {
  private statistics: GameStatistics;
  private levelHistory: LevelPerformance[] = [];
  private sessionStartTime: number = Date.now();

  constructor() {
    this.statistics = this.loadStatistics();
  }

  // 加载统计数据
  private loadStatistics(): GameStatistics {
    const saved = localStorage.getItem('blueprint-game-stats');
    if (saved) {
      return JSON.parse(saved);
    }
    
    return {
      totalPlayTime: 0,
      levelsCompleted: 0,
      totalConnections: 0,
      averageCompletionTime: 0,
      bestScore: 0,
      totalScore: 0,
      perfectLevels: 0,
      efficiency: 0,
      nodeTypesUsed: {},
      portTypesConnected: {},
      mistakeCount: 0,
      hintsUsed: 0
    };
  }

  // 保存统计数据
  private saveStatistics(): void {
    localStorage.setItem('blueprint-game-stats', JSON.stringify(this.statistics));
  }

  // 记录关卡完成
  recordLevelCompletion(
    level: number,
    score: number,
    completionTime: number,
    nodes: GameNode[],
    connections: Connection[],
    mistakes: number = 0,
    hintsUsed: number = 0
  ): void {
    const efficiency = this.calculateEfficiency(nodes, connections, mistakes);
    const complexity = this.calculateComplexity(nodes, connections);
    
    const performance: LevelPerformance = {
      levelNumber: level,
      completionTime,
      score,
      mistakes,
      hintsUsed,
      efficiency,
      complexity,
      timestamp: Date.now()
    };
    
    this.levelHistory.push(performance);
    this.updateStatistics(performance, nodes, connections);
    this.saveStatistics();
  }

  // 更新统计数据
  private updateStatistics(
    performance: LevelPerformance,
    nodes: GameNode[],
    connections: Connection[]
  ): void {
    this.statistics.levelsCompleted++;
    this.statistics.totalConnections += connections.length;
    this.statistics.totalScore += performance.score;
    this.statistics.mistakeCount += performance.mistakes;
    this.statistics.hintsUsed += performance.hintsUsed;
    
    if (performance.score > this.statistics.bestScore) {
      this.statistics.bestScore = performance.score;
    }
    
    if (performance.mistakes === 0) {
      this.statistics.perfectLevels++;
    }
    
    // 更新平均完成时间
    const totalTime = this.statistics.averageCompletionTime * (this.statistics.levelsCompleted - 1) + performance.completionTime;
    this.statistics.averageCompletionTime = totalTime / this.statistics.levelsCompleted;
    
    // 更新效率
    const totalEfficiency = this.statistics.efficiency * (this.statistics.levelsCompleted - 1) + performance.efficiency;
    this.statistics.efficiency = totalEfficiency / this.statistics.levelsCompleted;
    
    // 统计节点类型使用
    nodes.forEach(node => {
      const key = node.type;
      this.statistics.nodeTypesUsed[key] = (this.statistics.nodeTypesUsed[key] || 0) + 1;
    });
    
    // 统计端口类型连接
    connections.forEach(connection => {
      const fromPort = nodes.flatMap(n => [...n.inputPorts, ...n.outputPorts])
        .find(p => p.id === connection.fromPortId);
      if (fromPort) {
        const key = `${fromPort.shape}-${fromPort.color}`;
        this.statistics.portTypesConnected[key] = (this.statistics.portTypesConnected[key] || 0) + 1;
      }
    });
  }

  // 计算效率
  private calculateEfficiency(nodes: GameNode[], connections: Connection[], mistakes: number): number {
    const totalPorts = nodes.reduce((sum, node) => 
      sum + node.inputPorts.length + node.outputPorts.length, 0
    );
    const connectionRatio = connections.length / Math.max(totalPorts / 2, 1);
    const mistakePenalty = Math.max(0, 1 - mistakes * 0.1);
    
    return Math.min(100, connectionRatio * mistakePenalty * 100);
  }

  // 计算复杂度
  private calculateComplexity(nodes: GameNode[], connections: Connection[]): number {
    const nodeCount = nodes.length;
    const connectionCount = connections.length;
    const portVariety = new Set(
      nodes.flatMap(n => [...n.inputPorts, ...n.outputPorts])
        .map(p => `${p.shape}-${p.color}`)
    ).size;
    
    return nodeCount + connectionCount + portVariety;
  }

  // 获取统计数据
  getStatistics(): GameStatistics {
    // 更新总游戏时间
    this.statistics.totalPlayTime += Date.now() - this.sessionStartTime;
    this.sessionStartTime = Date.now();
    
    return { ...this.statistics };
  }

  // 获取关卡历史
  getLevelHistory(): LevelPerformance[] {
    return [...this.levelHistory];
  }

  // 获取最近表现
  getRecentPerformance(count: number = 5): LevelPerformance[] {
    return this.levelHistory.slice(-count);
  }

  // 获取进步趋势
  getProgressTrend(): {
    scoreImprovement: number;
    timeImprovement: number;
    efficiencyImprovement: number;
  } {
    if (this.levelHistory.length < 2) {
      return { scoreImprovement: 0, timeImprovement: 0, efficiencyImprovement: 0 };
    }
    
    const recent = this.levelHistory.slice(-5);
    const earlier = this.levelHistory.slice(-10, -5);
    
    if (earlier.length === 0) {
      return { scoreImprovement: 0, timeImprovement: 0, efficiencyImprovement: 0 };
    }
    
    const recentAvg = {
      score: recent.reduce((sum, p) => sum + p.score, 0) / recent.length,
      time: recent.reduce((sum, p) => sum + p.completionTime, 0) / recent.length,
      efficiency: recent.reduce((sum, p) => sum + p.efficiency, 0) / recent.length
    };
    
    const earlierAvg = {
      score: earlier.reduce((sum, p) => sum + p.score, 0) / earlier.length,
      time: earlier.reduce((sum, p) => sum + p.completionTime, 0) / earlier.length,
      efficiency: earlier.reduce((sum, p) => sum + p.efficiency, 0) / earlier.length
    };
    
    return {
      scoreImprovement: ((recentAvg.score - earlierAvg.score) / earlierAvg.score) * 100,
      timeImprovement: ((earlierAvg.time - recentAvg.time) / earlierAvg.time) * 100,
      efficiencyImprovement: ((recentAvg.efficiency - earlierAvg.efficiency) / earlierAvg.efficiency) * 100
    };
  }

  // 重置统计数据
  resetStatistics(): void {
    this.statistics = {
      totalPlayTime: 0,
      levelsCompleted: 0,
      totalConnections: 0,
      averageCompletionTime: 0,
      bestScore: 0,
      totalScore: 0,
      perfectLevels: 0,
      efficiency: 0,
      nodeTypesUsed: {},
      portTypesConnected: {},
      mistakeCount: 0,
      hintsUsed: 0
    };
    this.levelHistory = [];
    this.saveStatistics();
  }

  // 导出数据
  exportData(): string {
    return JSON.stringify({
      statistics: this.statistics,
      levelHistory: this.levelHistory
    }, null, 2);
  }

  // 导入数据
  importData(data: string): boolean {
    try {
      const parsed = JSON.parse(data);
      if (parsed.statistics && parsed.levelHistory) {
        this.statistics = parsed.statistics;
        this.levelHistory = parsed.levelHistory;
        this.saveStatistics();
        return true;
      }
    } catch (error) {
      console.error('Failed to import data:', error);
    }
    return false;
  }
}

// 导出单例实例
export const gameAnalytics = new GameAnalytics();
