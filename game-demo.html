<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>蓝图连接游戏 - 演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }
        
        .demo-container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            max-width: 1000px;
            width: 95%;
            text-align: center;
        }
        
        h1 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 2.5em;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .subtitle {
            color: #7f8c8d;
            font-size: 1.2em;
            margin-bottom: 30px;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin: 40px 0;
        }
        
        .feature-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 25px;
            border-radius: 15px;
            border-left: 5px solid #667eea;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.1);
        }
        
        .feature-card h3 {
            color: #2980b9;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .feature-card p {
            color: #666;
            font-size: 14px;
            line-height: 1.6;
        }
        
        .feature-icon {
            font-size: 2em;
            margin-bottom: 10px;
            display: block;
        }
        
        .demo-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin: 40px 0;
            flex-wrap: wrap;
        }
        
        .demo-button {
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            min-width: 150px;
        }
        
        .primary-button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }
        
        .primary-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        
        .secondary-button {
            background: #ecf0f1;
            color: #2c3e50;
            border: 2px solid #bdc3c7;
        }
        
        .secondary-button:hover {
            background: #d5dbdb;
            border-color: #95a5a6;
        }
        
        .game-preview {
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
            border: 2px dashed #dee2e6;
        }
        
        .preview-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            align-items: center;
        }
        
        .preview-image {
            width: 100%;
            height: 200px;
            background: linear-gradient(45deg, #f1f3f4, #e8eaed);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 18px;
            position: relative;
            overflow: hidden;
        }
        
        .node-demo {
            position: absolute;
            width: 80px;
            height: 40px;
            background: #4CAF50;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: bold;
        }
        
        .node-start {
            top: 20px;
            left: 20px;
            background: #2196F3;
        }
        
        .node-end {
            bottom: 20px;
            right: 20px;
            background: #FF5722;
        }
        
        .connection-line {
            position: absolute;
            width: 2px;
            height: 100px;
            background: #667eea;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(45deg);
        }
        
        .features-list {
            text-align: left;
            margin: 20px 0;
        }
        
        .features-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
        }
        
        .features-list li:last-child {
            border-bottom: none;
        }
        
        .emoji {
            font-size: 1.2em;
            margin-right: 12px;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
            background: #d4edda;
            color: #155724;
        }
        
        @media (max-width: 768px) {
            .preview-content {
                grid-template-columns: 1fr;
            }
            
            .demo-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1>🎮 蓝图连接游戏</h1>
        <p class="subtitle">类似Unreal Engine蓝图系统的节点连接益智游戏</p>
        
        <div class="game-preview">
            <h3>游戏预览 <span class="status-badge">已完成</span></h3>
            <div class="preview-content">
                <div class="preview-image">
                    <div class="node-demo node-start">起点</div>
                    <div class="connection-line"></div>
                    <div class="node-demo node-end">终点</div>
                    <div style="position: absolute; bottom: 10px; font-size: 14px; color: #999;">
                        拖拽连接节点端口
                    </div>
                </div>
                <div class="features-list">
                    <h4>🎯 核心特性</h4>
                    <ul>
                        <li><span class="emoji">🔗</span>智能端口连接系统</li>
                        <li><span class="emoji">🎨</span>多种端口形状和颜色</li>
                        <li><span class="emoji">🧠</span>保证可解的关卡生成</li>
                        <li><span class="emoji">⚡</span>实时连接预览</li>
                        <li><span class="emoji">🎮</span>双游戏模式</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="features-grid">
            <div class="feature-card">
                <span class="feature-icon">🔧</span>
                <h3>智能节点系统</h3>
                <p>支持起点、终点和处理节点，每个节点可以有多个不同形状和颜色的端口。只有相同类型的端口才能连接。</p>
            </div>
            
            <div class="feature-card">
                <span class="feature-icon">🎯</span>
                <h3>双游戏模式</h3>
                <p>俄罗斯方块模式：节点自动掉落，时间压力挑战<br>回合制模式：策略性思考，无时间限制</p>
            </div>
            
            <div class="feature-card">
                <span class="feature-icon">🧠</span>
                <h3>智能算法</h3>
                <p>使用先进的图论算法确保生成的每个关卡都有解，包括环检测、拓扑排序和可达性分析。</p>
            </div>
            
            <div class="feature-card">
                <span class="feature-icon">🎨</span>
                <h3>视觉反馈</h3>
                <p>实时连接预览、端口高亮提示、数据流动画效果，提供直观的视觉反馈。</p>
            </div>
        </div>
        
        <div class="demo-buttons">
            <a href="./dist/index.html" class="demo-button primary-button" target="_blank">
                🚀 开始游戏
            </a>
            <button class="demo-button secondary-button" onclick="showFeatures()">
                📋 查看特性
            </button>
            <button class="demo-button secondary-button" onclick="showInstructions()">
                📖 游戏说明
            </button>
        </div>
        
        <div id="info-panel" style="display: none; margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 10px; text-align: left;">
            <!-- 动态内容将在这里显示 -->
        </div>
    </div>

    <script>
        function showFeatures() {
            const panel = document.getElementById('info-panel');
            panel.innerHTML = `
                <h3>🎮 游戏特性详解</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-top: 15px;">
                    <div>
                        <h4>🔗 端口连接规则</h4>
                        <ul>
                            <li>相同形状和颜色的端口才能连接</li>
                            <li>输出端口(右侧)连接到输入端口(左侧)</li>
                            <li>每个端口只能有一个连接</li>
                            <li>新连接会替换旧连接</li>
                        </ul>
                    </div>
                    <div>
                        <h4>🎯 游戏目标</h4>
                        <ul>
                            <li>连接所有起点到终点</li>
                            <li>确保所有端口都已连接</li>
                            <li>避免形成循环依赖</li>
                            <li>创建有效的数据流路径</li>
                        </ul>
                    </div>
                    <div>
                        <h4>🧠 智能特性</h4>
                        <ul>
                            <li>自动生成可解关卡</li>
                            <li>实时验证连接有效性</li>
                            <li>智能端口高亮提示</li>
                            <li>数据流动画演示</li>
                        </ul>
                    </div>
                </div>
            `;
            panel.style.display = 'block';
        }
        
        function showInstructions() {
            const panel = document.getElementById('info-panel');
            panel.innerHTML = `
                <h3>📖 游戏操作说明</h3>
                <div style="margin-top: 15px;">
                    <h4>🖱️ 基本操作</h4>
                    <ol style="margin: 10px 0; padding-left: 20px;">
                        <li><strong>拖拽节点</strong>：点击并拖拽节点可以移动位置</li>
                        <li><strong>连接端口</strong>：从输出端口(右侧)拖拽到输入端口(左侧)</li>
                        <li><strong>删除连接</strong>：点击连接线可以删除连接</li>
                        <li><strong>放置节点</strong>：从右侧临时区选择节点，然后点击画布放置</li>
                    </ol>
                    
                    <h4>🎮 游戏模式</h4>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li><strong>回合制模式</strong>：无时间限制，策略性解决谜题</li>
                        <li><strong>俄罗斯方块模式</strong>：节点自动掉落，增加时间压力</li>
                    </ul>
                    
                    <h4>🏆 获胜条件</h4>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li>所有端口都已正确连接</li>
                        <li>形成有效的数据流路径</li>
                        <li>没有循环依赖</li>
                        <li>所有终点节点都可达</li>
                    </ul>
                </div>
            `;
            panel.style.display = 'block';
        }
        
        // 添加页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.feature-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });
    </script>
</body>
</html>
